[[{"meta_info_type": "学科领域", "meta_info_desc": "研究集中在在线学习和教育心理学领域", "meta_info_content": "This paper presents a personal account of developments in research on online learning over the past 30 years.", "classified": ["在线学习", "教育心理学"], "parent_cls_dict": {"在线学习": "在线学习", "教育心理学": "教育心理学"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究涉及在线学习和数字学习环境", "meta_info_content": "Online learning (also referred to as e-learning, digital learning, or computer-based learning).", "classified": ["在线学习环境", "数字学习环境"], "parent_cls_dict": {"在线学习环境": "在线学习", "数字学习环境": "在线学习"}}, {"meta_info_type": "研究目的", "meta_info_desc": "提供在线学习研究发展的个人历史回顾", "meta_info_content": "The goal of this review is to provide a personal history of developments in research on online learning over the past 30 years.", "classified": ["在线学习", "历史回顾"], "parent_cls_dict": {"在线学习": "在线学习", "历史回顾": "历史"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注在线学习、认知负荷理论、多媒体学习认知理论", "meta_info_content": "Contributes to the science of learning (as exemplified by developments in cognitive load theory, the cognitive theory of multimedia learning).", "classified": ["在线学习", "认知负荷理论", "多媒体学习认知理论"], "parent_cls_dict": {"在线学习": "在线学习", "认知负荷理论": "教育心理学", "多媒体学习认知理论": "教育心理学"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于认知心理学和多媒体学习的认知理论", "meta_info_content": "The science of learning (i.e., how people learn), the science of instruction (i.e., how to help people learn).", "classified": ["认知心理学", "多媒体学习"], "parent_cls_dict": {"认知心理学": "心理学", "多媒体学习": "学习理论"}}, {"meta_info_type": "数据来源", "meta_info_desc": "使用自我报告调查、保留测试、多层次转移测试和学习过程中的日志文件数据", "meta_info_content": "Supplementing self‐report surveys and retention tests with multilevel transfer tests, log file data during learning.", "classified": ["自我报告", "测试数据", "日志文件"], "parent_cls_dict": {"自我报告": "调查数据", "测试数据": "评估数据", "日志文件": "系统数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用认知神经科学措施进行认知处理分析", "meta_info_content": "Cognitive neuroscience measures of cognitive processing during learning.", "classified": ["认知神经科学", "认知分析"], "parent_cls_dict": {"认知神经科学": "神经科学", "认知分析": "分析技术"}}, {"meta_info_type": "主要发现", "meta_info_desc": "学习是由教学方法而非教学媒介引起的，应关注数字学习环境的独特特征", "meta_info_content": "Learning is caused by instructional methods rather than instructional media, so research should focus on features uniquely afforded by digital learning environments.", "classified": ["教学方法", "数字学习环境"], "parent_cls_dict": {"教学方法": "教学", "数字学习环境": "在线学习"}}, {"meta_info_type": "局限性", "meta_info_desc": "需要继续研究以确定哪些教学技术最有效", "meta_info_content": "Research in online learning should identify boundary conditions under which instructional techniques are most effective.", "classified": ["研究需求", "教学技术"], "parent_cls_dict": {"研究需求": "研究", "教学技术": "教育技术"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为大学一年级的商业和经济学专业学生", "meta_info_content": "This study was conducted in an 8-week-long introductory mathematics course for first-year business and economics students at a Dutch university.", "classified": ["大学生", "高等教育"], "parent_cls_dict": {"大学生": "高等教育", "高等教育": "高等教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在数学领域", "meta_info_content": "The study was conducted on 1,072 students over an 8-week-long introductory mathematics course.", "classified": ["数学"], "parent_cls_dict": {"数学": "数学"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "学习环境为混合学习模式，使用SOWISO和MyStatLab数字平台", "meta_info_content": "The course followed a blended learning format, combining face-to-face tutorials with self-study activities using two digital learning platforms: SOWISO and MyStatLab.", "classified": ["混合学习环境", "SOWISO平台", "MyStatLab平台"], "parent_cls_dict": {"混合学习环境": "混合学习", "SOWISO平台": "学习平台", "MyStatLab平台": "学习平台"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "共有1072名学生参与研究", "meta_info_content": "The study was conducted on 1,072 students over an 8-week-long introductory mathematics course.", "classified": ["学生", "参与人数"], "parent_cls_dict": {"学生": "参与者", "参与人数": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "调查学生在自然学习环境下对反馈策略的偏好以及其影响", "meta_info_content": "This study investigates different profiles of students' learning behaviors based on clustering learning dispositions, prior knowledge, and the choice of feedback strategies in a naturalistic setting.", "classified": ["学生偏好", "反馈策略"], "parent_cls_dict": {"学生偏好": "教育研究", "反馈策略": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注个体差异和反馈策略在学习过程中的作用", "meta_info_content": "The study focuses on individual differences in the use of instructional scaffolds, such as worked examples and hints.", "classified": ["个体差异", "反馈策略"], "parent_cls_dict": {"个体差异": "教育心理学", "反馈策略": "教育方法"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于自我调节学习理论和例题学习的支架作用", "meta_info_content": "Theoretical background includes Self-Regulated Learning (SRL) and its scaffolds of examples and hints.", "classified": ["自我调节学习", "例题学习"], "parent_cls_dict": {"自我调节学习": "学习理论", "例题学习": "学习方法"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源包括追踪数据、自我报告调查和表现数据", "meta_info_content": "Data were collected from three main sources: Trace Data, Self-Report Surveys, and Performance Data.", "classified": ["追踪数据", "自我报告", "表现数据"], "parent_cls_dict": {"追踪数据": "系统数据", "自我报告": "调查数据", "表现数据": "评估数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用K-means聚类分析识别学习者档案", "meta_info_content": "K-means cluster analysis was used to identify six learning profiles based on trace data, learning dispositions, and prior knowledge.", "classified": ["K-means聚类", "学习者档案分析"], "parent_cls_dict": {"K-means聚类": "聚类算法", "学习者档案分析": "学习分析"}}, {"meta_info_type": "主要发现", "meta_info_desc": "发现六种不同学习档案，学习成效与档案密切相关", "meta_info_content": "Six distinct profiles were identified, and course performance was strongly related to profile membership.", "classified": ["学习档案", "学习成效"], "parent_cls_dict": {"学习档案": "学习行为", "学习成效": "教育结果"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究涉及医学教育中的住院医师培训阶段", "meta_info_content": "With the implementation of competency-based medical education (CBME) in emergency medicine, residency programs will amass substantial amounts of qualitative and quantitative data about trainees’ performances.", "classified": ["住院医师", "医学教育"], "parent_cls_dict": {"住院医师": "医学教育", "医学教育": "医学教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在医学教育领域，特别是急诊医学", "meta_info_content": "In medicine, this is evidenced by the exponential growth in analytics-related literature... Similarly, CBME frameworks such as the Accreditation Council for Graduate Medical Education (ACGME) Milestones and the Royal College of Physicians and Surgeons of Canada’s Competence by Design have made large volumes of trainee performance data available.", "classified": ["医学教育", "急诊医学"], "parent_cls_dict": {"医学教育": "医学", "急诊医学": "医学"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "医疗学习分析在住院医师培训项目中应用", "meta_info_content": "At a local training-program level, medical learning analytics has the potential to assist program directors and competency committees with interpreting assessment data to inform decision-making.", "classified": ["医疗学习分析", "住院医师培训"], "parent_cls_dict": {"医疗学习分析": "学习分析", "住院医师培训": "医学教育"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "涉及多个住院医师培训项目", "meta_info_content": "Most residency programs struggle to implement programmatic assessment due to insufficient data collection, outdated analytic techniques, and inadequate data representation.", "classified": ["住院医师", "项目"], "parent_cls_dict": {"住院医师": "参与者", "项目": "项目"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在探索医学教育中的学习分析方法及其对评估系统的指导潜力", "meta_info_content": "The purpose of this review is to characterize the methodologies of learning analytics and explore their potential to guide new forms of assessment within medical education.", "classified": ["医学教育", "学习分析", "评估系统"], "parent_cls_dict": {"医学教育": "医学", "学习分析": "数据分析", "评估系统": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注学习分析在住院医师评估中的应用", "meta_info_content": "Learning analytics applies statistical and computational methods to describe, characterize, and predict learning behaviors.", "classified": ["学习分析", "住院医师评估"], "parent_cls_dict": {"学习分析": "数据分析", "住院医师评估": "医学"}}, {"meta_info_type": "理论基础", "meta_info_desc": "强调数据分析需以教育理论为指导以确保结果解释的准确性", "meta_info_content": "Educators must ensure that data analyses are guided by educational theory to ask meaningful questions and interpret results appropriately.", "classified": ["教育理论", "数据分析"], "parent_cls_dict": {"教育理论": "教育学", "数据分析": "数据科学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来自住院医师表现的定性和定量数据", "meta_info_content": "Residency programs will amass substantial amounts of qualitative and quantitative data about trainees’ performances.", "classified": ["定性数据", "定量数据"], "parent_cls_dict": {"定性数据": "研究数据", "定量数据": "研究数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用描述性、解释性、预测性和评估性分析技术", "meta_info_content": "Learning analytics techniques are categorized into descriptive, explanatory, predictive, and evaluative analyses.", "classified": ["描述性分析", "解释性分析", "预测性分析", "评估性分析"], "parent_cls_dict": {"描述性分析": "数据分析", "解释性分析": "数据分析", "预测性分析": "数据分析", "评估性分析": "数据分析"}}, {"meta_info_type": "模型评估方法", "meta_info_desc": "使用机器学习算法进行预测和筛查", "meta_info_content": "Machine learning algorithms (MLAs) are revolutionizing data interpretation by identifying patterns and making predictions.", "classified": ["机器学习"], "parent_cls_dict": {"机器学习": "机器学习"}}, {"meta_info_type": "局限性", "meta_info_desc": "机器学习算法无法理解个体的优劣势或制定具体的补救计划", "meta_info_content": "MLAs lack the sophistication to understand individual strengths and weaknesses or generate specific remediation plans.", "classified": ["机器学习局限", "个性化"], "parent_cls_dict": {"机器学习局限": "AI", "个性化": "个性化"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为本科医学专业学生", "meta_info_content": "The study included second- and third-year undergraduate medical students (n = 104–120 per cohort) enrolled in the Bachelor of Medicine, Bachelor of Surgery (MBBS) program.", "classified": ["本科生", "医学教育"], "parent_cls_dict": {"本科生": "高等教育", "医学教育": "医学教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在医学教育中的解剖学领域", "meta_info_content": "Key words: undergraduate medical education, human anatomy, dissection, e-learning, multimedia, learning analytics.", "classified": ["医学教育", "解剖学"], "parent_cls_dict": {"医学教育": "医学", "解剖学": "医学"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "使用学习管理系统（LMS）提供的音视频资源", "meta_info_content": "DAVR were delivered via the learning management system (LMS) approximately one week before the corresponding dissection sessions.", "classified": ["学习管理系统"], "parent_cls_dict": {"学习管理系统": "学习管理"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "每个年级约有104到120名学生参与研究", "meta_info_content": "The study included second- and third-year undergraduate medical students (n = 104–120 per cohort).", "classified": ["学生", "参与人数"], "parent_cls_dict": {"学生": "参与者", "参与人数": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在提升医学本科生解剖学课前准备和学习效果", "meta_info_content": "In an attempt to improve undergraduate medical student preparation for and learning from dissection sessions, dissection audio-visual resources (DAVR) were developed.", "classified": ["医学教育", "解剖学", "学习效果"], "parent_cls_dict": {"医学教育": "医学", "解剖学": "医学", "学习效果": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注音视频资源对解剖学学习准备和效果的影响", "meta_info_content": "Hypothesis 1: DAVR would improve students’ self-reported preparedness and confidence during dissection.", "classified": ["音视频资源", "解剖学学习"], "parent_cls_dict": {"音视频资源": "教育技术", "解剖学学习": "医学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "通过学习管理系统数据和学生问卷调查收集数据", "meta_info_content": "LMS data were used to track the number and duration of DAVR views. A survey was distributed to collect student feedback on DAVR.", "classified": ["学习管理系统", "问卷调查"], "parent_cls_dict": {"学习管理系统": "系统数据", "问卷调查": "调查数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用t检验、ANOVA、Pearson相关和偏相关分析", "meta_info_content": "Statistical tests included t-tests, ANOVA, Pearson correlation, and partial correlation to evaluate relationships between DAVR usage and student outcomes.", "classified": ["t检验", "ANOVA", "Pearson相关", "偏相关分析"], "parent_cls_dict": {"t检验": "统计分析", "ANOVA": "统计分析", "Pearson相关": "统计分析", "偏相关分析": "统计分析"}}, {"meta_info_type": "主要发现", "meta_info_desc": "DAVR提高了学生的准备度，但对解剖学成绩影响有限", "meta_info_content": "Ninety percent of survey respondents agreed that DAVR improved their preparation for and learning from dissection. Cadaveric anatomy scores were 3.9% lower in Year 2 and 0.3% lower in Year 3 with DAVR compared to previous years.", "classified": ["学生准备度", "解剖学成绩"], "parent_cls_dict": {"学生准备度": "学习准备", "解剖学成绩": "医学"}}, {"meta_info_type": "局限性", "meta_info_desc": "研究设计为准实验设计，资源制作质量有限", "meta_info_content": "Limitations of the study include the quasi-experimental design and the modest production quality of DAVR.", "classified": ["研究设计", "资源质量"], "parent_cls_dict": {"研究设计": "研究", "资源质量": "资源管理"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为医学本科生，平均年龄约21岁", "meta_info_content": "The study population consisted mainly of females (65.3% in MA Group, 60.4% in CA Group, 75.5% in MA + CA Group). The average age was approximately 21 years.", "classified": ["本科生", "医学教育"], "parent_cls_dict": {"本科生": "高等教育", "医学教育": "医学教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在医学教育，特别是解剖学课程", "meta_info_content": "Anatomy is an essential subject of the medical curriculum.", "classified": ["医学教育", "解剖学"], "parent_cls_dict": {"医学教育": "医学", "解剖学": "医学"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "使用VIMU在线平台进行计算机辅助学习", "meta_info_content": "VIMU, an e-learning online platform, was used as a CAL tool.", "classified": ["VIMU平台", "计算机辅助学习"], "parent_cls_dict": {"VIMU平台": "学习平台", "计算机辅助学习": "教育技术"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "共有611名医学学生参与研究", "meta_info_content": "This study involved 611 medical students from the Faculty of Medicine, University of Porto.", "classified": ["医学学生", "参与人数"], "parent_cls_dict": {"医学学生": "参与者", "参与人数": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在了解计算机辅助学习平台如何影响医学学生的解剖学表现", "meta_info_content": "This study aimed to understand how training with CAL platforms influences medical students’ anatomy performance.", "classified": ["计算机辅助学习", "医学教育", "解剖学"], "parent_cls_dict": {"计算机辅助学习": "教育技术", "医学教育": "医学", "解剖学": "医学"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注计算机辅助学习对解剖学学习效果的影响", "meta_info_content": "The findings suggest that CAL platforms positively influence anatomy learning outcomes.", "classified": ["计算机辅助学习", "解剖学学习"], "parent_cls_dict": {"计算机辅助学习": "教育技术", "解剖学学习": "医学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来自参与学生的培训和考试表现", "meta_info_content": "Students accessed 15 MA training sessions and 12 CA training sessions over three weeks. Training was voluntary and formative, with performance analyzed in subsequent practical exams.", "classified": ["培训数据", "考试表现"], "parent_cls_dict": {"培训数据": "教育活动", "考试表现": "评估数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用Pearson相关系数和多元线性回归分析数据", "meta_info_content": "Data were analyzed using SPSS. Correlations were assessed using <PERSON>’s coefficient, and multiple linear regression models identified factors influencing anatomy performance.", "classified": ["Pearson相关", "多元线性回归"], "parent_cls_dict": {"Pearson相关": "统计分析", "多元线性回归": "回归分析"}}, {"meta_info_type": "主要发现", "meta_info_desc": "发现计算机辅助学习课程与解剖学表现之间存在显著正相关", "meta_info_content": "A strong positive correlation was observed between the number of CAL sessions attended and anatomy performance in all groups.", "classified": ["计算机辅助学习", "解剖学表现"], "parent_cls_dict": {"计算机辅助学习": "教育技术", "解剖学表现": "医学"}}, {"meta_info_type": "局限性", "meta_info_desc": "研究没有设置对照组且难以测量外部学习努力", "meta_info_content": "The absence of a control group and difficulty in measuring external learning efforts were limitations.", "classified": ["研究设计", "测量挑战"], "parent_cls_dict": {"研究设计": "研究", "测量挑战": "评估"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为高等教育学生", "meta_info_content": "Higher education institutions are mining and analyzing student data...", "classified": ["高等教育"], "parent_cls_dict": {"高等教育": "高等教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在教育数据挖掘和学习分析领域", "meta_info_content": "Educational Data Mining and Learning Analytics", "classified": ["教育数据挖掘", "学习分析"], "parent_cls_dict": {"教育数据挖掘": "数据分析", "学习分析": "数据分析"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "使用学习管理系统（LMS）如Canvas和Blackboard", "meta_info_content": "learning management systems (LMSs) like Canvas and Blackboard...", "classified": ["学习管理系统", "Canvas平台", "Blackboard平台"], "parent_cls_dict": {"学习管理系统": "学习管理", "Canvas平台": "学习平台", "Blackboard平台": "学习平台"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "Unizin联盟包括25所大学，服务超过900,000名学生", "meta_info_content": "Unizin, an institutional consortium of 25 public universities... serves over 900,000 students...", "classified": ["大学", "学生"], "parent_cls_dict": {"大学": "机构", "学生": "参与者"}}, {"meta_info_type": "研究目的", "meta_info_desc": "探讨高等教育机构作为信息受托人的责任", "meta_info_content": "We argue that higher education institutions are paradigms of information fiduciaries.", "classified": ["高等教育", "信息责任"], "parent_cls_dict": {"高等教育": "教育", "信息责任": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注学习分析对学生隐私和数据使用的影响", "meta_info_content": "Learning analytics raises serious issues concerning student privacy...", "classified": ["学习分析", "学生隐私", "数据使用"], "parent_cls_dict": {"学习分析": "数据分析", "学生隐私": "数据伦理", "数据使用": "数据伦理"}}, {"meta_info_type": "数据来源", "meta_info_desc": "使用学生人口统计、学术表现、在线和离线行为等数据", "meta_info_content": "surface sensitive data and information about... a student’s demographics, academic performance...", "classified": ["人口统计", "学术表现", "行为数据"], "parent_cls_dict": {"人口统计": "人口数据", "学术表现": "评估数据", "行为数据": "行为数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用预测分析支持学生指导", "meta_info_content": "Some systems use predictive analytics to support advising...", "classified": ["预测分析", "学生指导"], "parent_cls_dict": {"预测分析": "数据分析", "学生指导": "教育技术"}}, {"meta_info_type": "主要发现", "meta_info_desc": "发现高等教育机构需要优先考虑学生利益并保护隐私", "meta_info_content": "HEIs must recognize their role as information fiduciaries and adopt LA practices...", "classified": ["学生利益", "隐私保护"], "parent_cls_dict": {"学生利益": "学生权益", "隐私保护": "伦理问题"}}, {"meta_info_type": "局限性", "meta_info_desc": "数据使用可能与学生利益不一致，影响信任", "meta_info_content": "FERPA’s broad definition of “school officials” allows third-party vendors to access student data...", "classified": ["数据使用", "信任问题"], "parent_cls_dict": {"数据使用": "数据管理", "信任问题": "信任"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为高等教育本科生", "meta_info_content": "This study reports findings from over 100 interviews with undergraduate students across eight U.S. higher education institutions.", "classified": ["本科生", "高等教育"], "parent_cls_dict": {"本科生": "高等教育", "高等教育": "高等教育"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究关注高等教育机构中学习分析的使用", "meta_info_content": "Higher education institutions are increasingly adopting learning analytics (LA), a sociotechnical data-mining and analytic practice.", "classified": ["高等教育", "学习分析"], "parent_cls_dict": {"高等教育": "教育领域", "学习分析": "数据分析"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "共有105名本科生参与了半结构化访谈", "meta_info_content": "A total of 105 semi-structured interviews were conducted to explore students' perceptions of privacy and LA practices.", "classified": ["本科生", "参与人数"], "parent_cls_dict": {"本科生": "参与者", "参与人数": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在填补学生对学习分析隐私问题的认知空白", "meta_info_content": "This study seeks to fill this gap by examining student perspectives on LA practices, with a focus on privacy expectations, data usage, and trust.", "classified": ["学习分析", "隐私问题"], "parent_cls_dict": {"学习分析": "数据分析", "隐私问题": "伦理"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注学生隐私、知情同意和机构信任", "meta_info_content": "Students express concerns about data transparency, ethical usage, and the potential for unfair treatment arising from LA initiatives.", "classified": ["学生隐私", "知情同意", "机构信任"], "parent_cls_dict": {"学生隐私": "数据伦理", "知情同意": "数据伦理", "机构信任": "数据伦理"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来自8所美国高等教育机构的学生访谈", "meta_info_content": "This study recruited undergraduate students from eight U.S. higher education institutions.", "classified": ["访谈数据"], "parent_cls_dict": {"访谈数据": "调查数据"}}, {"meta_info_type": "数据粒度", "meta_info_desc": "以学生个人为单位进行访谈和分析", "meta_info_content": "Interviews were conducted face-to-face, via phone, or web conferencing, and audio recordings were transcribed for analysis.", "classified": ["学生个体"], "parent_cls_dict": {"学生个体": "个体"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "采用定性编码方法分析访谈记录", "meta_info_content": "Transcripts were analyzed using qualitative coding methods to identify themes and patterns.", "classified": ["定性编码", "访谈记录分析"], "parent_cls_dict": {"定性编码": "定性分析", "访谈记录分析": "信息处理"}}, {"meta_info_type": "主要发现", "meta_info_desc": "学生普遍缺乏对学习分析实践的认知，并对隐私表达了深刻关注", "meta_info_content": "Students lacked awareness of LA practices and expressed confusion about why institutions collect and analyze their data.", "classified": ["学习分析认知", "隐私关注"], "parent_cls_dict": {"学习分析认知": "数据分析", "隐私关注": "伦理问题"}}, {"meta_info_type": "局限性", "meta_info_desc": "未详细说明学习分析如何具体改善教育", "meta_info_content": "They often speculated that LA could improve education but lacked specific knowledge of how it works.", "classified": ["学习分析", "改善效果"], "parent_cls_dict": {"学习分析": "数据分析", "改善效果": "教育效果"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为使用智能辅导系统的学生", "meta_info_content": "Data from 103 students were used in this analysis.", "classified": ["不确定教育阶段"], "parent_cls_dict": {"不确定教育阶段": "不确定教育阶段"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在计算机系统课程的教育领域", "meta_info_content": "The proposed ITCS domain is a computer systems course that covers computer architecture and organization.", "classified": ["计算机系统", "教育"], "parent_cls_dict": {"计算机系统": "计算机科学", "教育": "教育"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "使用基于网络的智能辅导系统进行研究", "meta_info_content": "A web-based intelligent tutoring system designed by the authors.", "classified": ["智能辅导系统"], "parent_cls_dict": {"智能辅导系统": "学习技术"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "数据来自103名学生", "meta_info_content": "Data from 103 students were used in this analysis.", "classified": ["学生", "数据规模"], "parent_cls_dict": {"学生": "参与者", "数据规模": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在通过聚类和可视化分析学生多维度的概念成功率", "meta_info_content": "The objectives of this study are to cluster the data available from an Intelligent Tutoring System (ITS) and to visualize the multidimensional data analysis results.", "classified": ["聚类分析", "可视化", "学生表现"], "parent_cls_dict": {"聚类分析": "数据分析", "可视化": "数据分析", "学生表现": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注多维度数据聚类和可视化", "meta_info_content": "Cluster analysis results are visualized using a parallel coordinate system.", "classified": ["多维度数据聚类", "数据可视化"], "parent_cls_dict": {"多维度数据聚类": "数据分析", "数据可视化": "数据分析"}}, {"meta_info_type": "数据来源", "meta_info_desc": "来自智能辅导系统的数据，包括学生概念成功率", "meta_info_content": "Data collected from a web-based intelligent tutoring system designed by the authors.", "classified": ["智能辅导系统"], "parent_cls_dict": {"智能辅导系统": "系统数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用k-means和模糊c-means聚类算法进行数据分析", "meta_info_content": "In this study, the ITS data are clustered using k-means and fuzzy c-means algorithms.", "classified": ["k-means聚类", "模糊c-means聚类"], "parent_cls_dict": {"k-means聚类": "聚类算法", "模糊c-means聚类": "聚类算法"}}, {"meta_info_type": "模型评估方法", "meta_info_desc": "通过平行坐标可视化比较两种聚类算法的聚类性能", "meta_info_content": "The clustering performance of the two algorithms is compared... Clustering results are visualized using PARVIS.", "classified": ["可视化", "聚类"], "parent_cls_dict": {"可视化": "数据分析", "聚类": "数据分析"}}, {"meta_info_type": "主要发现", "meta_info_desc": "聚类揭示了学生在课程概念上的表现差异，帮助教师识别学生学习困难的概念", "meta_info_content": "The results demonstrate that both algorithms produce similar patterns. For instance, students who were successful in most concepts were grouped together, while students struggling with specific concepts formed distinct clusters.", "classified": ["课程概念表现差异", "学习困难识别"], "parent_cls_dict": {"课程概念表现差异": "学习行为", "学习困难识别": "教学"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为大学生", "meta_info_content": "predict the marks that university students will obtain in the final exam of a course.", "classified": ["大学生", "高等教育"], "parent_cls_dict": {"大学生": "高等教育", "高等教育": "高等教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在教育数据挖掘领域", "meta_info_content": "Keywords: educational data mining, classifying students, predicting marks.", "classified": ["教育数据挖掘"], "parent_cls_dict": {"教育数据挖掘": "数据分析"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "使用Moodle作为学习管理系统", "meta_info_content": "Several Cordoba University Moodle courses in engineering.", "classified": ["<PERSON><PERSON><PERSON>平台"], "parent_cls_dict": {"Moodle平台": "学习平台"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "实验使用了438个数据实例", "meta_info_content": "All available data: 438 instances with 9 input attributes.", "classified": ["数据实例"], "parent_cls_dict": {"数据实例": "数据"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在通过数据挖掘预测学生的最终成绩", "meta_info_content": "This paper shows how web usage mining can be applied in e-learning systems to predict the marks.", "classified": ["数据挖掘", "学生成绩预测"], "parent_cls_dict": {"数据挖掘": "数据分析", "学生成绩预测": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注分类算法和学生成绩预测", "meta_info_content": "The performance of different data mining techniques for classifying students are compared.", "classified": ["分类算法", "学生成绩预测"], "parent_cls_dict": {"分类算法": "数据分析", "学生成绩预测": "教育评估"}}, {"meta_info_type": "数据来源", "meta_info_desc": "来源于Moodle课程的使用数据", "meta_info_content": "student’s usage data in several Cordoba University Moodle courses.", "classified": ["Moodle数据"], "parent_cls_dict": {"Moodle数据": "系统数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用多种分类算法，包括决策树和神经网络", "meta_info_content": "Several well-known classification methods have been used, such as statistical methods, decision trees, rule and fuzzy rule induction methods, and neural networks.", "classified": ["分类算法", "决策树", "神经网络"], "parent_cls_dict": {"分类算法": "分类技术", "决策树": "分类技术", "神经网络": "机器学习"}}, {"meta_info_type": "模型评估方法", "meta_info_desc": "使用分类准确率评估模型性能", "meta_info_content": "Results showed that classification accuracy was highest when all data was utilized.", "classified": ["分类准确率"], "parent_cls_dict": {"分类准确率": "模型评估"}}, {"meta_info_type": "Type of Output/Artifact", "meta_info_desc": "开发了一个专用的Moodle挖掘工具", "meta_info_content": "We have also developed a specific Moodle mining tool.", "classified": ["工具"], "parent_cls_dict": {"工具": "技术工具"}}, {"meta_info_type": "目标用户", "meta_info_desc": "目标用户包括教育工作者和课程管理员", "meta_info_content": "oriented for the use of not only experts in data mining but also newcomers like instructors and courseware authors.", "classified": ["教育者", "管理员"], "parent_cls_dict": {"教育者": "教育相关人员", "管理员": "管理相关人员"}}, {"meta_info_type": "主要发现", "meta_info_desc": "所有数据使用能够实现更高的分类准确率", "meta_info_content": "utilizing all available data generally yields better results.", "classified": ["数据使用", "分类准确率"], "parent_cls_dict": {"数据使用": "数据处理", "分类准确率": "数据分析"}}, {"meta_info_type": "局限性", "meta_info_desc": "需要预处理技术来提高分类准确率", "meta_info_content": "pre-processing techniques like discretization and rebalancing can significantly affect classification accuracy.", "classified": ["数据预处理", "分类准确性"], "parent_cls_dict": {"数据预处理": "数据处理", "分类准确性": "AI"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象包括中学生和工程专业学生", "meta_info_content": "Two experiments were conducted: 1. Middle school students: G1: 16 students using Moodle. G2: 16 students using Moodle with Cloud-AWAS. 2. Engineering students: G3: 30 students using Moodle. G4: 30 students using Moodle with Cloud-AWAS.", "classified": ["中学生", "大学生", "工程教育"], "parent_cls_dict": {"中学生": "中学教育", "大学生": "高等教育", "工程教育": "工程教育"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究在云服务环境中实施，并集成到多个学习管理系统中", "meta_info_content": "Cloud-AWAS adapts the e-assessment process for each learner using their behavior data. It is integrated into several Learning Management Systems (LMS), including Moodle, OLAT, and LAMS.", "classified": ["云服务环境", "学习管理系统"], "parent_cls_dict": {"云服务环境": "技术环境", "学习管理系统": "学习管理"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "共有92名学生参与实验", "meta_info_content": "Two experiments were conducted: 1. Middle school students: G1: 16 students, G2: 16 students. 2. Engineering students: G3: 30 students, G4: 30 students.", "classified": ["学生", "参与人数"], "parent_cls_dict": {"学生": "参与者", "参与人数": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "开发适应性电子评估系统以优化学习者的评估流程", "meta_info_content": "In this paper, we present a Cloud Adapted Workflow e-Assessment System called Cloud-AWAS. This system makes use of learning analytics to turn learners into more effective and better learners.", "classified": ["电子评估", "评估优化"], "parent_cls_dict": {"电子评估": "教育技术", "评估优化": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注学习分析和适应性电子评估", "meta_info_content": "Keywords: learning analytics; adaptation; learner profile; workflow; ontology", "classified": ["学习分析", "适应性电子评估"], "parent_cls_dict": {"学习分析": "数据分析", "适应性电子评估": "教育评估"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源于个人数据、电子评估数据和日志文件数据", "meta_info_content": "The learner profile ontology is based on three types of data: personal data, log file data, and e-assessment data.", "classified": ["个人数据", "电子评估", "日志文件"], "parent_cls_dict": {"个人数据": "个人数据", "电子评估": "评估数据", "日志文件": "系统数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用学习者档案本体进行数据分析和评估", "meta_info_content": "The adaptation process involves the development of the learner profile ontology, which is used to adapt the e-assessment workflow.", "classified": ["学习者档案本体", "数据分析", "评估"], "parent_cls_dict": {"学习者档案本体": "学习分析", "数据分析": "数据分析", "评估": "数据分析"}}, {"meta_info_type": "主要发现", "meta_info_desc": "使用Cloud-AWAS的学习者在测试分数和满意度上显著提高", "meta_info_content": "Learners using Cloud-AWAS achieved significantly higher scores compared to those using Moodle alone. Questionnaire results indicated high satisfaction with Cloud-AWAS.", "classified": ["测试分数", "满意度"], "parent_cls_dict": {"测试分数": "教育结果", "满意度": "学习体验"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为大学一年级工程学生", "meta_info_content": "First-year engineering students often struggle with basic subjects like Physics.", "classified": ["大学生", "工程教育"], "parent_cls_dict": {"大学生": "高等教育", "工程教育": "工程教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在物理教育领域", "meta_info_content": "This study evaluates the introduction of MOOC technology in a remedial Physics course for engineering students.", "classified": ["物理教育"], "parent_cls_dict": {"物理教育": "科学教育"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "使用Khan Academy和Moodle平台进行混合学习", "meta_info_content": "The Khan Academy platform was adapted and personalized for this experience... Moodle was used to complement the platform by offering forums and course structure.", "classified": ["Khan Academy平台", "<PERSON><PERSON><PERSON>平台", "混合学习环境"], "parent_cls_dict": {"Khan Academy平台": "学习平台", "Moodle平台": "学习平台", "混合学习环境": "混合学习"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "参与者被分为实验组和对照组进行比较", "meta_info_content": "Students in the experimental group (using MOOC technology) performed significantly better than those in the control group (traditional teaching).", "classified": ["实验组", "对照组"], "parent_cls_dict": {"实验组": "实验", "对照组": "实验"}}, {"meta_info_type": "研究目的", "meta_info_desc": "评估MOOC技术在翻转课堂中的应用效果", "meta_info_content": "This paper analyzes the effectiveness of MOOC technology in a Physics SPOC, using flipping the classroom methodology.", "classified": ["MOOC", "翻转课堂"], "parent_cls_dict": {"MOOC": "在线学习", "翻转课堂": "教育技术"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注翻转课堂和MOOC技术对学习的影响", "meta_info_content": "One popular application of MOOCs is flipping the classroom... This paper analyzes the effectiveness of MOOC technology in a Physics SPOC.", "classified": ["翻转课堂", "MOOC技术"], "parent_cls_dict": {"翻转课堂": "教育方法", "MOOC技术": "在线学习"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源于学生成绩和满意度调查", "meta_info_content": "Students interacted with the Khan Academy platform... An online survey was conducted to assess students’ satisfaction with the MOOC-driven experience.", "classified": ["成绩数据", "满意度调查"], "parent_cls_dict": {"成绩数据": "评估数据", "满意度调查": "调查数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用独立样本t检验分析成绩差异", "meta_info_content": "The experimental group had a mean grade of 7.50 compared to 5.43 for the control group, with an independent t-test confirming statistical significance (p < 0.0001).", "classified": ["独立样本t检验", "成绩差异分析"], "parent_cls_dict": {"独立样本t检验": "统计分析", "成绩差异分析": "教育分析"}}, {"meta_info_type": "主要发现", "meta_info_desc": "MOOC技术显著提高学生成绩和满意度", "meta_info_content": "The introduction of MOOCs technology significantly improved students' grades and satisfaction while fostering effective interactions.", "classified": ["MOOC技术", "学生成绩", "满意度"], "parent_cls_dict": {"MOOC技术": "在线学习", "学生成绩": "教育结果", "满意度": "学习体验"}}, {"meta_info_type": "局限性", "meta_info_desc": "研究建议在不同情境中进一步评估MOOC的有效性", "meta_info_content": "Future studies should explore adaptive learning and evaluate MOOCs in different contexts to optimize their effectiveness.", "classified": ["MOOC评估", "情境多样性"], "parent_cls_dict": {"MOOC评估": "在线学习", "情境多样性": "教育情境"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象主要为本科生", "meta_info_content": "Additionally, 183 undergraduate students completed a questionnaire about their learning habits, motivations, and attitudes toward MOOCs.", "classified": ["本科生", "高等教育"], "parent_cls_dict": {"本科生": "高等教育", "高等教育": "高等教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在计算机科学教育领域", "meta_info_content": "Our primary goal is to perform a detailed and comprehensive investigation of learning behaviors in MOOCs... in a computer science course.", "classified": ["计算机科学教育"], "parent_cls_dict": {"计算机科学教育": "计算机科学"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究在MOOCs环境下进行", "meta_info_content": "Massive open online courses (MOOCs) are the latest e-learning initiative to attain widespread popularity globally.", "classified": ["MOOC环境"], "parent_cls_dict": {"MOOC环境": "在线学习"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "共有1783名学生参与，其中1190名来自深圳大学", "meta_info_content": "Daily activity data from 1,783 participants, including 1,190 from Shenzhen University, was recorded.", "classified": ["学生", "参与人数"], "parent_cls_dict": {"学生": "参与者", "参与人数": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在详细调查MOOCs中的学习行为并识别未解决的问题", "meta_info_content": "Our primary goal is to perform a detailed and comprehensive investigation of learning behaviors in MOOCs and identify unresolved issues.", "classified": ["MOOC", "学习行为"], "parent_cls_dict": {"MOOC": "在线学习", "学习行为": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注学习行为和学习风格的关系", "meta_info_content": "Despite evidence suggesting that learning styles influence learning behaviors and achievement, there remains limited research exploring online learning activities among students with different learning styles.", "classified": ["学习行为", "学习风格"], "parent_cls_dict": {"学习行为": "教育心理学", "学习风格": "教育心理学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来自问卷调查和日常活动记录", "meta_info_content": "We employed commonly used educational data mining methodologies to analyze and interpret behaviors... based on questionnaire survey data and daily activity data.", "classified": ["问卷调查", "活动记录"], "parent_cls_dict": {"问卷调查": "调查数据", "活动记录": "行为数据"}}, {"meta_info_type": "数据粒度", "meta_info_desc": "基于学生个体行为数据的分析", "meta_info_content": "Recorded daily interactions, including visits, posts, reviews, and examination grades.", "classified": ["学生个体"], "parent_cls_dict": {"学生个体": "个体"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用聚类、分类和人工神经网络进行数据分析", "meta_info_content": "Three models were employed: Clustering (k-means), Classification (Support Vector Machine), Artificial Neural Networks.", "classified": ["聚类", "分类", "人工神经网络"], "parent_cls_dict": {"聚类": "聚类算法", "分类": "分类技术", "人工神经网络": "机器学习"}}, {"meta_info_type": "模型评估方法", "meta_info_desc": "使用SVM和ANN预测学习风格的高准确性", "meta_info_content": "Using SVM and ANN models, learning styles were predicted with high accuracy (SVM: 99.79%, ANN: 100%).", "classified": ["SVM", "ANN"], "parent_cls_dict": {"SVM": "机器学习", "ANN": "机器学习"}}, {"meta_info_type": "主要发现", "meta_info_desc": "学习风格是影响MOOCs中学习行为的重要因素", "meta_info_content": "Our findings reveal that most students can be divided into groups consistent with their learning styles.", "classified": ["学习风格", "MOOCs学习行为"], "parent_cls_dict": {"学习风格": "学习行为", "MOOCs学习行为": "在线学习"}}, {"meta_info_type": "局限性", "meta_info_desc": "研究对象主要为深圳大学本科生，样本同质性限制了研究的普适性", "meta_info_content": "This study is limited by the homogeneity of the participants, primarily undergraduates from Shenzhen University.", "classified": ["样本同质性", "研究普适性"], "parent_cls_dict": {"样本同质性": "研究", "研究普适性": "研究"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为参与计算机网络课程的学生", "meta_info_content": "SocialWire has been used to teach a computer networking course over three consecutive academic years.", "classified": ["不确定教育阶段"], "parent_cls_dict": {"不确定教育阶段": "不确定教育阶段"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在计算机网络课程中的在线社交学习网络", "meta_info_content": "SocialWire has been used to teach a computer networking course.", "classified": ["计算机网络", "在线社交学习网络"], "parent_cls_dict": {"计算机网络": "计算机科学", "在线社交学习网络": "在线学习"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "使用SocialWire平台进行社交学习管理", "meta_info_content": "In this work, we study the nature and strength of associations between students using an online social network embedded in a learning management system.", "classified": ["SocialWire平台", "社交学习"], "parent_cls_dict": {"SocialWire平台": "学习平台", "社交学习": "学习形式"}}, {"meta_info_type": "研究目的", "meta_info_desc": "研究学生在在线社交网络中的关联性及其对学业成绩的影响", "meta_info_content": "We study the nature and strength of associations between students using an online social network embedded in a learning management system.", "classified": ["在线社交网络", "学生成绩"], "parent_cls_dict": {"在线社交网络": "在线学习", "学生成绩": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注社交图的结构属性、学生间的协作模式及学业成绩影响因素", "meta_info_content": "Identify structural properties of the social graph, patterns of collaboration among students, and factors influencing final academic achievements.", "classified": ["社交图", "协作模式", "学业成绩影响因素"], "parent_cls_dict": {"社交图": "数据分析", "协作模式": "教育方法", "学业成绩影响因素": "教育评估"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来自三学年的课程中学生发布的问题和答案", "meta_info_content": "With datasets from three offerings of the same course, we mined the sequences of questions and answers posted by the students.", "classified": ["课程问答"], "parent_cls_dict": {"课程问答": "教育活动"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用社交网络分析技术和统计学习技术进行分析", "meta_info_content": "We apply social network analysis (SNA) techniques and propose a learning failure prediction method based on centrality values and statistical learning techniques.", "classified": ["社交网络分析", "统计学习"], "parent_cls_dict": {"社交网络分析": "网络分析", "统计学习": "机器学习"}}, {"meta_info_type": "模型评估方法", "meta_info_desc": "使用逻辑回归、线性判别分析和支持向量机进行学习失败预测", "meta_info_content": "A learning failure prediction model was developed using logistic regression, linear discriminant analysis, and support vector machines.", "classified": ["逻辑回归", "线性判别分析", "支持向量机"], "parent_cls_dict": {"逻辑回归": "机器学习", "线性判别分析": "机器学习", "支持向量机": "机器学习"}}, {"meta_info_type": "主要发现", "meta_info_desc": "发现活跃参与社交网络的学生在学业成绩上表现优异", "meta_info_content": "Centrality measures were found to correlate with final academic performance, particularly out-degree and eigenvector centrality.", "classified": ["社交网络参与", "学业成绩"], "parent_cls_dict": {"社交网络参与": "学习行为", "学业成绩": "教育结果"}}], [{"meta_info_type": "学科领域", "meta_info_desc": "研究集中在教育中的大数据与学习分析领域", "meta_info_content": "This paper considers data science and the significance of Big Data and Learning Analytics in education.", "classified": ["教育", "大数据", "学习分析"], "parent_cls_dict": {"教育": "教育", "大数据": "数据分析", "学习分析": "数据分析"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究涉及互联网、移动计算、云计算技术对教育数据的影响", "meta_info_content": "The Internet, mobile computing, and cloud computing have profoundly changed the way data are perceived and utilized.", "classified": ["互联网技术", "移动计算", "云计算"], "parent_cls_dict": {"互联网技术": "技术环境", "移动计算": "技术环境", "云计算": "技术环境"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "研究讨论了大规模开放在线课程（MOOCs）", "meta_info_content": "The widespread adoption of digital tools in education, such as learner records, sensors, and Massive Open Online Courses (MOOCs)...", "classified": ["MOOCs"], "parent_cls_dict": {"MOOCs": "在线学习"}}, {"meta_info_type": "研究目的", "meta_info_desc": "探讨大数据和学习分析在教育中的潜力，以改善学习结果和管理效率", "meta_info_content": "Big data and learning analytics have the potential to transform education by improving learner outcomes, enhancing administrative decision-making...", "classified": ["大数据", "学习分析", "教育管理"], "parent_cls_dict": {"大数据": "数据分析", "学习分析": "数据分析", "教育管理": "教育研究"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来自多种来源并经过聚合和清理", "meta_info_content": "Data Capture and Collection: Aggregates and cleans data from diverse sources.", "classified": ["聚合数据"], "parent_cls_dict": {"聚合数据": "处理数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用Hadoop、Hive、Pig等工具进行大数据处理", "meta_info_content": "Several tools have been developed to address these challenges: <PERSON><PERSON>, <PERSON><PERSON>, Pig, <PERSON><PERSON>, HBase...", "classified": ["<PERSON><PERSON>", "Hive", "Pig", "大数据处理"], "parent_cls_dict": {"Hadoop": "大数据工具", "Hive": "大数据工具", "Pig": "大数据工具", "大数据处理": "数据处理"}}, {"meta_info_type": "主要发现", "meta_info_desc": "大数据可以改善反馈、个性化教学、行政效率、跟踪监控以及跨学科合作", "meta_info_content": "Big data provides numerous benefits in education, including improved feedback, enhanced personalization, administrative efficiency, tracking and monitoring, collaboration and interdisciplinary learning.", "classified": ["大数据应用", "个性化教学", "行政效率", "跨学科合作"], "parent_cls_dict": {"大数据应用": "数据分析", "个性化教学": "教学", "行政效率": "教育管理", "跨学科合作": "学术合作"}}, {"meta_info_type": "局限性", "meta_info_desc": "实施大数据技术面临财务成本、隐私问题、技术专业知识和数据安全挑战", "meta_info_content": "Despite its potential, implementing big data in education presents challenges: Financial Costs, Privacy Concerns, Technical Expertise, Data Security.", "classified": ["大数据挑战", "财务成本", "隐私问题", "技术知识", "数据安全"], "parent_cls_dict": {"大数据挑战": "数据分析", "财务成本": "资源管理", "隐私问题": "隐私", "技术知识": "技术", "数据安全": "安全"}}], [{"meta_info_type": "学习环境与平台", "meta_info_desc": "研究在混合学习环境中进行，使用Moodle作为学习管理系统", "meta_info_content": "The paper suggests the implementation of association analysis for improving the process of e-testing in a blended learning environment.", "classified": ["混合学习环境", "<PERSON><PERSON><PERSON>平台"], "parent_cls_dict": {"混合学习环境": "混合学习", "Moodle平台": "学习平台"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在计算机图形学课程的电子测试", "meta_info_content": "Research was conducted using knowledge tests from the Computer Graphics Moodle Course.", "classified": ["计算机图形学", "电子测试"], "parent_cls_dict": {"计算机图形学": "计算机科学", "电子测试": "教育技术"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在通过关联分析改进电子测试过程", "meta_info_content": "The paper suggests the implementation of association analysis for improving the process of e-testing.", "classified": ["关联分析", "电子测试"], "parent_cls_dict": {"关联分析": "数据分析", "电子测试": "教育技术"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注关联规则挖掘在教育数据中的应用", "meta_info_content": "The core concept of association rules involves discovering correlations in itemsets to identify significant patterns.", "classified": ["关联规则挖掘", "教育数据"], "parent_cls_dict": {"关联规则挖掘": "数据分析", "教育数据": "数据分析"}}, {"meta_info_type": "数据来源", "meta_info_desc": "使用Moodle系统中的知识测试数据", "meta_info_content": "The study analyzed data from Moodle tests conducted as part of a blended learning program.", "classified": ["Moodle数据", "测试数据"], "parent_cls_dict": {"Moodle数据": "系统数据", "测试数据": "评估数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用Apriori和Predictive Apriori算法发现关联规则", "meta_info_content": "By implementing Apriori and Predictive Apriori algorithms, numerous association rules were discovered.", "classified": ["Apriori算法", "Predictive Apriori算法", "关联规则"], "parent_cls_dict": {"Apriori算法": "关联规则挖掘", "Predictive Apriori算法": "关联规则挖掘", "关联规则": "关联规则挖掘"}}, {"meta_info_type": "主要发现", "meta_info_desc": "发现模拟测试与正式测试结果之间的关系", "meta_info_content": "The discovered rules revealed relationships between mock and official test results.", "classified": ["模拟测试", "正式测试结果"], "parent_cls_dict": {"模拟测试": "测试", "正式测试结果": "教育结果"}}, {"meta_info_type": "局限性", "meta_info_desc": "未来研究将关注方法的普适性和扩展到其他数据集", "meta_info_content": "Future research will focus on generalizing the methodology and exploring additional datasets.", "classified": ["研究普适性", "数据集扩展"], "parent_cls_dict": {"研究普适性": "研究", "数据集扩展": "数据管理"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为本科生", "meta_info_content": "The SocialWire platform was used for teaching an undergraduate computer networking course.", "classified": ["本科生", "高等教育"], "parent_cls_dict": {"本科生": "高等教育", "高等教育": "高等教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在计算机网络领域", "meta_info_content": "We report its use over two academic years in a computer networking course for undergraduates.", "classified": ["计算机网络"], "parent_cls_dict": {"计算机网络": "计算机科学"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "使用社交学习平台SocialWire", "meta_info_content": "This paper describes SocialWire, a social learning environment (SLE) designed to provide a complete social learning paradigm.", "classified": ["SocialWire平台", "社交学习"], "parent_cls_dict": {"SocialWire平台": "学习平台", "社交学习": "学习形式"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "参与人数约180至182名学生", "meta_info_content": "The course included cohort sizes of 180–182 over two academic years.", "classified": ["学生", "参与人数"], "parent_cls_dict": {"学生": "参与者", "参与人数": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "预测学生学习成功与失败", "meta_info_content": "We propose a learning success/failure prediction method using statistical learning techniques.", "classified": ["学生成绩预测"], "parent_cls_dict": {"学生成绩预测": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注学习活动的节奏作为预测学生表现的关键特征", "meta_info_content": "The pace of events a student engages in is a better predictor of their final grade than the type of events or activities.", "classified": ["学习活动节奏", "学生表现预测"], "parent_cls_dict": {"学习活动节奏": "教育心理学", "学生表现预测": "教育评估"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来自社交学习平台上的学生活动记录", "meta_info_content": "Our learning platform, SocialWire, collects a detailed record of students' activity.", "classified": ["社交学习平台"], "parent_cls_dict": {"社交学习平台": "系统数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用逻辑回归、线性判别分析和支持向量机进行预测", "meta_info_content": "Using logistic regression (LR), linear discriminant analysis (LDA), and support vector machines (SVM), we evaluated the predictive power of selected features.", "classified": ["逻辑回归", "线性判别分析", "支持向量机"], "parent_cls_dict": {"逻辑回归": "预测技术", "线性判别分析": "预测技术", "支持向量机": "机器学习"}}, {"meta_info_type": "模型评估方法", "meta_info_desc": "使用k折交叉验证评估模型性能", "meta_info_content": "The models were trained and tested using k-fold cross-validation.", "classified": ["k折交叉验证"], "parent_cls_dict": {"k折交叉验证": "模型评估"}}, {"meta_info_type": "主要发现", "meta_info_desc": "学习活动的节奏是学生成功的重要预测因子", "meta_info_content": "Our study demonstrates that the pace of learning activities, as measured by event slope (SE), is a significant predictor of student success.", "classified": ["学习活动节奏", "学生成功"], "parent_cls_dict": {"学习活动节奏": "学习行为", "学生成功": "教育结果"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为在线本科课程学生", "meta_info_content": "evaluated in two fully online undergraduate courses.", "classified": ["本科生", "高等教育"], "parent_cls_dict": {"本科生": "高等教育", "高等教育": "高等教育"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究在在线学习环境中进行", "meta_info_content": "online learning environments", "classified": ["在线学习环境"], "parent_cls_dict": {"在线学习环境": "在线学习"}}, {"meta_info_type": "研究目的", "meta_info_desc": "评估个人信息系统Glance在在线学习环境中支持自我调节的效果", "meta_info_content": "This paper presents an educational PI system called Glance and evaluates its usefulness in two fully online undergraduate courses.", "classified": ["在线学习", "自我调节"], "parent_cls_dict": {"在线学习": "在线学习", "自我调节": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注自我调节学习和个人信息系统", "meta_info_content": "self-regulated learning, personal informatics systems", "classified": ["自我调节学习", "个人信息系统"], "parent_cls_dict": {"自我调节学习": "教育心理学", "个人信息系统": "教育技术"}}, {"meta_info_type": "数据来源", "meta_info_desc": "通过网络问卷收集学生数据", "meta_info_content": "A web-based questionnaire was used to collect data from students.", "classified": ["网络问卷"], "parent_cls_dict": {"网络问卷": "调查数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "应用信息可视化技术支持自我调节学习", "meta_info_content": "Information visualization techniques, as a subset of LA, are becoming increasingly relevant.", "classified": ["信息可视化", "自我调节学习"], "parent_cls_dict": {"信息可视化": "数据可视化", "自我调节学习": "教育技术"}}, {"meta_info_type": "主要发现", "meta_info_desc": "发现Glance系统有助于提高学生对课程情况的意识，支持自我调节", "meta_info_content": "The respondents indicated that <PERSON><PERSON> helped them be aware of their situation in the course, supporting self-regulation.", "classified": ["Glance系统", "课程意识", "自我调节"], "parent_cls_dict": {"Glance系统": "教育技术", "课程意识": "学习行为", "自我调节": "学习行为"}}, {"meta_info_type": "局限性", "meta_info_desc": "需要进一步研究PI系统对学生表现的影响及探索定制化和指导功能", "meta_info_content": "Further research is needed to measure the impact of PI systems on students' performance and to explore features like customization and guidance.", "classified": ["PI系统研究", "学生表现", "定制化"], "parent_cls_dict": {"PI系统研究": "研究", "学生表现": "教育效果", "定制化": "个性化"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为大学毕业生", "meta_info_content": "Data from 1,633 and 1,606 graduates in the academic years of 2013 and 2014, respectively, were collected.", "classified": ["大学毕业生", "高等教育"], "parent_cls_dict": {"大学毕业生": "高等教育", "高等教育": "高等教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在以能力为基础的课程中", "meta_info_content": "A competency-based curriculum involves an outcome-based approach for cultivating graduates’ core competencies required for specific professions.", "classified": ["能力为基础课程"], "parent_cls_dict": {"能力为基础课程": "教育"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "使用YZU的VACC系统进行学习分析", "meta_info_content": "The Visualized Analytics of Core Competencies (VACC) system is a learning analytics system used at Yuan Ze University (YZU) since 2013.", "classified": ["VACC系统", "学习分析"], "parent_cls_dict": {"VACC系统": "学习平台", "学习分析": "数据分析"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "研究包含1633名和1606名毕业生", "meta_info_content": "Data from 1,633 and 1,606 graduates in the academic years of 2013 and 2014, respectively, were collected.", "classified": ["毕业生", "参与人数"], "parent_cls_dict": {"毕业生": "参与者", "参与人数": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在通过学习分析协助课程委员会反思和调整能力为基础的课程", "meta_info_content": "This study proposes competency-based learning analytics tools to analyze curricula and graduates’ academic records of coursework, ultimately providing systematic evaluative information to assist curriculum committees.", "classified": ["学习分析", "课程调整"], "parent_cls_dict": {"学习分析": "数据分析", "课程调整": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "以能力为基础的课程和学习分析", "meta_info_content": "Learning analytics—an emerging, data-driven analysis of educational data—could be applied to assist curriculum committees in reflection.", "classified": ["能力为基础课程", "学习分析"], "parent_cls_dict": {"能力为基础课程": "教育方法", "学习分析": "数据分析"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源于毕业生的学业记录和课程数据", "meta_info_content": "Data from 1,633 and 1,606 graduates in the academic years of 2013 and 2014, respectively, were collected.", "classified": ["学业记录", "课程数据"], "parent_cls_dict": {"学业记录": "评估数据", "课程数据": "教育活动"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用VACC系统的七种分析工具进行数据分析", "meta_info_content": "The analytics include seven tools: CBCC Table, Quantity Radar Chart, Quality Radar Chart, etc.", "classified": ["VACC系统", "数据分析工具"], "parent_cls_dict": {"VACC系统": "教育工具", "数据分析工具": "分析技术"}}, {"meta_info_type": "主要发现", "meta_info_desc": "通过学习分析，课程委员会能够识别课程缺陷、调节干预措施并评估其效果", "meta_info_content": "This study demonstrates how competency-based learning analytics can assist curriculum committees in systematically reflecting and regulating curricula, faculty teaching, and student learning.", "classified": ["课程缺陷识别", "干预措施调节", "效果评估"], "parent_cls_dict": {"课程缺陷识别": "教学", "干预措施调节": "教学", "效果评估": "数据分析"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为高等教育阶段的学生", "meta_info_content": "The dataset was created using data from the Moodle platform, traditional classroom activities, and the institution's information system.", "classified": ["高等教育"], "parent_cls_dict": {"高等教育": "高等教育"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究在混合学习环境中进行，涉及Moodle平台和传统课堂活动", "meta_info_content": "The dataset was created using data from the Moodle platform, traditional classroom activities, and the institution's information system.", "classified": ["混合学习环境", "<PERSON><PERSON><PERSON>平台", "传统课堂"], "parent_cls_dict": {"混合学习环境": "混合学习", "Moodle平台": "学习平台", "传统课堂": "线下学习"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "数据集包含276个实例", "meta_info_content": "It contains 276 instances, each with 16 features representing student activities, and a class label indicating the final grade.", "classified": ["数据实例"], "parent_cls_dict": {"数据实例": "数据"}}, {"meta_info_type": "研究目的", "meta_info_desc": "提高混合学习环境中学生最终成绩预测的准确性", "meta_info_content": "The objective is to improve the accuracy of final grade predictions.", "classified": ["混合学习", "学生成绩预测"], "parent_cls_dict": {"混合学习": "教育技术", "学生成绩预测": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注分类与预测学生成绩的模型", "meta_info_content": "Classification is one of the major research problems in EDM.", "classified": ["分类模型", "学生成绩预测"], "parent_cls_dict": {"分类模型": "数据分析", "学生成绩预测": "教育评估"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源于Moodle平台、课堂活动和信息系统", "meta_info_content": "The dataset was created using data from the Moodle platform, traditional classroom activities, and the institution's information system.", "classified": ["Moodle数据", "课堂活动", "信息系统"], "parent_cls_dict": {"Moodle数据": "系统数据", "课堂活动": "教育活动", "信息系统": "系统数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用多种分类器和集成方法提高预测准确性", "meta_info_content": "The ensemble combines <PERSON><PERSON><PERSON>, Hidden Naïve <PERSON>, J48 decision tree, and Random Forest classifiers.", "classified": ["分类器", "集成方法", "预测准确性"], "parent_cls_dict": {"分类器": "分类技术", "集成方法": "机器学习", "预测准确性": "数据分析"}}, {"meta_info_type": "模型评估方法", "meta_info_desc": "通过准确性、错误率和精度评估分类器性能", "meta_info_content": "Performance was measured using accuracy, error rates, and precision.", "classified": ["准确性", "错误率", "精度"], "parent_cls_dict": {"准确性": "模型评估", "错误率": "模型评估", "精度": "模型评估"}}, {"meta_info_type": "主要发现", "meta_info_desc": "集成模型实现了90.43%的分类准确率，显著提高预测性能", "meta_info_content": "The ensemble achieved a classification accuracy of 90.43%, significantly improving predictions across all class labels.", "classified": ["集成模型", "分类准确率", "预测性能"], "parent_cls_dict": {"集成模型": "数据分析", "分类准确率": "数据分析", "预测性能": "AI"}}], [{"meta_info_type": "学科领域", "meta_info_desc": "研究集中在工程教育中的学习分析和教育数据挖掘", "meta_info_content": "This study provides researchers with an overview of the progress made to date and identifies areas where research is missing in engineering education.", "classified": ["工程教育", "学习分析", "教育数据挖掘"], "parent_cls_dict": {"工程教育": "工程", "学习分析": "数据分析", "教育数据挖掘": "数据分析"}}, {"meta_info_type": "研究目的", "meta_info_desc": "系统性映射学习分析和教育数据挖掘在工程教育中的应用", "meta_info_content": "A systematic mapping study was carried out, classifying publications by research type and contribution.", "classified": ["学习分析", "教育数据挖掘", "工程教育"], "parent_cls_dict": {"学习分析": "数据分析", "教育数据挖掘": "数据分析", "工程教育": "教育"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注学习分析、教育数据挖掘在决策中的应用", "meta_info_content": "Learning analytics is a discipline that uses techniques, methods, and algorithms to discover and extract patterns in stored educational data with the purpose of improving the teaching-learning process.", "classified": ["学习分析", "教育数据挖掘", "决策应用"], "parent_cls_dict": {"学习分析": "数据分析", "教育数据挖掘": "数据分析", "决策应用": "教育管理"}}, {"meta_info_type": "数据来源", "meta_info_desc": "主要数据来源包括学习管理系统日志和数据库、机构数据库、特定数据集", "meta_info_content": "Main Data Sources (RQ1): LMS logs and databases: 34% of studies, Institutional databases: 29%, Specific datasets: 25%.", "classified": ["学习管理系统", "数据库", "数据集"], "parent_cls_dict": {"学习管理系统": "系统数据", "数据库": "系统数据", "数据集": "数据集"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用关联规则、聚类、分类、分析与可视化技术", "meta_info_content": "Methods and Techniques (RQ2): Association rules, clustering, and classification: 25%, Analysis and visualization: 19%.", "classified": ["关联规则", "聚类", "分类", "可视化技术"], "parent_cls_dict": {"关联规则": "关联规则挖掘", "聚类": "聚类算法", "分类": "分类技术", "可视化技术": "数据可视化"}}, {"meta_info_type": "模型评估方法", "meta_info_desc": "通过系统性映射研究进行评估", "meta_info_content": "This study follows a systematic mapping approach based on four stages: feasibility, planning, classification scheme, and results presentation.", "classified": ["系统性映射"], "parent_cls_dict": {"系统性映射": "研究方法"}}, {"meta_info_type": "主要发现", "meta_info_desc": "ICT相关领域主导应用，其他工程学科应用存在空白", "meta_info_content": "The results highlight the dominance of ICT-related fields in applying LA and EDM, revealing gaps in other engineering disciplines such as civil and environmental engineering.", "classified": ["ICT应用", "工程学科空白"], "parent_cls_dict": {"ICT应用": "信息技术", "工程学科空白": "工程"}}, {"meta_info_type": "局限性", "meta_info_desc": "非ICT工程领域和情感因素应用不足", "meta_info_content": "Although significant progress has been made, gaps persist in applying these methods to non-ICT engineering fields and addressing affective factors.", "classified": ["应用不足", "情感因素"], "parent_cls_dict": {"应用不足": "应用", "情感因素": "心理学"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为工程课程的学生", "meta_info_content": "We illustrate its use with students from engineering courses.", "classified": ["不确定教育阶段", "工程教育"], "parent_cls_dict": {"不确定教育阶段": "不确定教育阶段", "工程教育": "工程教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在工程教育中的翻转课堂方法", "meta_info_content": "Nowadays, there are several experiences of using the flipped classroom (FC) in engineering education due to its advantages over traditional methodologies.", "classified": ["工程教育", "翻转课堂"], "parent_cls_dict": {"工程教育": "工程", "翻转课堂": "教育技术"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究在翻转课堂中使用学习分析工具和GEL平台", "meta_info_content": "We developed a LA tool to support the proposed methodology by extending the functionality of the 'GEL' platform.", "classified": ["翻转课堂", "学习分析工具", "GEL平台"], "parent_cls_dict": {"翻转课堂": "学习形式", "学习分析工具": "数据分析", "GEL平台": "学习平台"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在通过学习分析改进翻转课堂方法", "meta_info_content": "We propose applying learning analytics (LA) to improve the methodology.", "classified": ["学习分析", "翻转课堂"], "parent_cls_dict": {"学习分析": "数据分析", "翻转课堂": "教育技术"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注翻转课堂、学习分析和学生的主动学习", "meta_info_content": "Keywords: Active learning, flipped classroom, interventions, learning analytics, methodology", "classified": ["翻转课堂", "学习分析", "主动学习"], "parent_cls_dict": {"翻转课堂": "教育方法", "学习分析": "数据分析", "主动学习": "教育心理学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来自学生与预备资源的互动", "meta_info_content": "The tool gathers data related to students' interactions with preparatory resources.", "classified": ["资源互动"], "parent_cls_dict": {"资源互动": "行为数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用学习分析技术优化翻转课堂", "meta_info_content": "Learning analytics (LA) can help address these issues by analyzing students' data and their learning contexts to optimize the FC methodology.", "classified": ["学习分析", "翻转课堂"], "parent_cls_dict": {"学习分析": "学习分析", "翻转课堂": "教育技术"}}, {"meta_info_type": "Type of Output/Artifact", "meta_info_desc": "产出一种支持翻转课堂的学习分析工具", "meta_info_content": "We present a LA tool designed and implemented for this methodology, allowing teachers to obtain relevant information for improving their FC experiences.", "classified": ["工具"], "parent_cls_dict": {"工具": "技术工具"}}, {"meta_info_type": "目标用户", "meta_info_desc": "目标用户为翻转课堂中的教师", "meta_info_content": "The tool provides several visualizations analyzed in detail, allowing teachers to obtain relevant information for improving their FC experiences.", "classified": ["教育者"], "parent_cls_dict": {"教育者": "教育相关人员"}}, {"meta_info_type": "主要发现", "meta_info_desc": "提出了一种通过学习分析技术改进翻转课堂的方法", "meta_info_content": "Our study contributes by proposing a methodology that defines specific actions teachers can take to enhance the FC through LA, supported by a tool designed for this purpose.", "classified": ["学习分析技术", "翻转课堂改进"], "parent_cls_dict": {"学习分析技术": "数据分析", "翻转课堂改进": "教学"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为计算机科学专业的大学生", "meta_info_content": "It is a MOOC of object-oriented modeling destined to computer science students.", "classified": ["大学生", "高等教育"], "parent_cls_dict": {"大学生": "高等教育", "高等教育": "高等教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在计算机科学中的面向对象建模领域", "meta_info_content": "The dataset used in this research is gathered from a MOOC entitled 'Initiation à la Conception Orientée Objet' (ICOO).", "classified": ["计算机科学", "面向对象建模"], "parent_cls_dict": {"计算机科学": "计算机科学", "面向对象建模": "计算机科学"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "学习环境为大规模开放在线课程（MOOC）平台", "meta_info_content": "Learning environments such as Massive Open Online Courses (MOOCs) generate a big amount of educational data.", "classified": ["MOOC平台"], "parent_cls_dict": {"MOOC平台": "在线学习"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "研究使用了包含超过750,000条学习和评估活动数据的日志文件", "meta_info_content": "The log file records more than 750,000 learning and assessment activities.", "classified": ["学习数据", "评估数据"], "parent_cls_dict": {"学习数据": "数据", "评估数据": "数据"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在改进现有数据模型以支持评估分析并提高学习过程的分析能力", "meta_info_content": "Our contribution allows enhancing and improving the existing data model by proposing an ontological data model supporting assessment analytics based on enriching the assessment result and adding assessment context.", "classified": ["数据模型", "评估分析"], "parent_cls_dict": {"数据模型": "数据分析", "评估分析": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注评估数据分析与现有学习分析数据模型的增强", "meta_info_content": "Tracking and analyzing assessment data during the learning process is presently significant and considered as a new research trend in an educational context.", "classified": ["评估数据分析", "学习分析数据模型"], "parent_cls_dict": {"评估数据分析": "数据分析", "学习分析数据模型": "数据分析"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于xAPI数据模型及语义网技术提出评估分析的本体模型", "meta_info_content": "Our proposed model for assessment analytics is an enhanced version of the existing xAPI data model. Using semantic web formalisms such as ontology and annotations offers several advantages like aggregation of scattered data in the web, reusability, and interoperability.", "classified": ["xAPI数据模型", "语义网技术"], "parent_cls_dict": {"xAPI数据模型": "数据技术", "语义网技术": "数据技术"}}, {"meta_info_type": "数据来源", "meta_info_desc": "从一个MOOC课程日志中提取学习和评估活动数据", "meta_info_content": "The dataset used in this research is gathered from a MOOC entitled 'Initiation à la Conception Orientée Objet' (ICOO).", "classified": ["MOOC数据"], "parent_cls_dict": {"MOOC数据": "系统数据"}}, {"meta_info_type": "数据粒度", "meta_info_desc": "以评估活动、评估结果和评估上下文为主要数据粒度", "meta_info_content": "We will extract the set of traces related to assessment such as assessment activities, assessment results, and assessment context from the log file.", "classified": ["评估活动", "评估结果", "评估上下文"], "parent_cls_dict": {"评估活动": "评估", "评估结果": "评估", "评估上下文": "评估"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用语义网技术将日志数据转换为OWL文件以支持本体分析", "meta_info_content": "We have to convert the log file of traces to an Ontology Web Language (OWL) file.", "classified": ["语义网", "日志数据处理", "本体分析"], "parent_cls_dict": {"语义网": "语义技术", "日志数据处理": "数据处理", "本体分析": "分析技术"}}, {"meta_info_type": "主要发现", "meta_info_desc": "提出了一个基于xAPI的增强评估分析本体模型，并验证其适用性", "meta_info_content": "Based on identified weaknesses of the xAPI specification, we propose an enhancement of its data model to support assessment analytics effectively.", "classified": ["xAPI本体模型", "评估分析"], "parent_cls_dict": {"xAPI本体模型": "数据分析", "评估分析": "数据分析"}}, {"meta_info_type": "局限性", "meta_info_desc": "框架结构不完全，开放性使其可以结合其他工具和实践", "meta_info_content": "Our proposal is near to be a framework and not a methodology because it has an incomplete structure which leaves room for other practices and tools to be included.", "classified": ["框架结构", "开放性"], "parent_cls_dict": {"框架结构": "结构", "开放性": "开放性"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为工程教育的本科生及其相关学术项目", "meta_info_content": "The dataset comprises 152 self-study reports (SSRs) of engineering programs accredited by ABET-EAC.", "classified": ["本科生", "工程教育"], "parent_cls_dict": {"本科生": "高等教育", "工程教育": "工程教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在工程教育领域", "meta_info_content": "This paper proposes a data mining-based approach to discover hidden knowledge about PEOs, SOs, and their mappings in engineering programs.", "classified": ["工程教育"], "parent_cls_dict": {"工程教育": "工程"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "研究涉及152个ABET认证的工程项目", "meta_info_content": "The dataset comprises 152 self-study reports (SSRs) of engineering programs accredited by ABET-EAC (2000–2017).", "classified": ["工程项目"], "parent_cls_dict": {"工程项目": "项目"}}, {"meta_info_type": "研究目的", "meta_info_desc": "研究旨在通过数据挖掘方法分析PEO-SO映射以支持决策制定", "meta_info_content": "This paper proposes a data mining-based approach to analyze PEO-SO mappings in engineering education, providing insights into their correlations and applications for informed decision-making.", "classified": ["数据挖掘", "决策支持"], "parent_cls_dict": {"数据挖掘": "数据分析", "决策支持": "数据分析"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注PEOs和SOs的映射及其关联", "meta_info_content": "The mapping of PEOs to SOs is critical for program success, necessitating a deep understanding of their intra- and inter-correlations for effective decision-making.", "classified": ["PEOs", "SOs"], "parent_cls_dict": {"PEOs": "教育管理", "SOs": "教育管理"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源于ABET认证的工程项目的自我研究报告", "meta_info_content": "The dataset comprises 152 self-study reports (SSRs) of engineering programs accredited by ABET-EAC (2000–2017).", "classified": ["自我研究报告"], "parent_cls_dict": {"自我研究报告": "调查数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "应用Apriori算法生成关联规则", "meta_info_content": "The Apriori algorithm was applied to the target datasets to generate association rules.", "classified": ["Apriori算法", "关联规则"], "parent_cls_dict": {"Apriori算法": "关联规则挖掘", "关联规则": "关联规则挖掘"}}, {"meta_info_type": "主要发现", "meta_info_desc": "识别出PEOs和SOs的关联及其聚类", "meta_info_content": "Analysis revealed three clusters of PEOs based on their correlations; ABET SOs were grouped into three clusters.", "classified": ["PEOs", "SOs关联", "聚类"], "parent_cls_dict": {"PEOs": "教育目标", "SOs关联": "教育目标", "聚类": "数据分析"}}, {"meta_info_type": "局限性", "meta_info_desc": "研究主要集中在工程教育领域，其他学科的应用尚待探索", "meta_info_content": "Future work will extend the approach to academic programs in other disciplines, further exploring its generalizability and impact.", "classified": ["领域集中", "跨学科应用"], "parent_cls_dict": {"领域集中": "教育", "跨学科应用": "教育"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为高等教育阶段的学生", "meta_info_content": "EDM applies data mining methods to analyze data from interactive learning environments, particularly in higher education.", "classified": ["高等教育"], "parent_cls_dict": {"高等教育": "高等教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在计算机科学和信息技术领域", "meta_info_content": "The proposed system, AGLS, combines gamification, classification, and adaptation techniques. The data structure course was used as a prototype.", "classified": ["计算机科学", "信息技术"], "parent_cls_dict": {"计算机科学": "计算机科学", "信息技术": "信息技术"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究在Moodle LMS平台上进行", "meta_info_content": "Lectures, assignments, and quizzes were uploaded to a Moodle LMS platform, which was gamified by incorporating various game elements.", "classified": ["<PERSON><PERSON><PERSON>平台"], "parent_cls_dict": {"Moodle平台": "学习平台"}}, {"meta_info_type": "研究目的", "meta_info_desc": "研究旨在通过自适应游戏化提高在线学习的参与度和学习效果", "meta_info_content": "This paper combines gamification techniques and EDM methods with adaptive learning to increase students' engagement and performance.", "classified": ["自适应游戏化", "在线学习"], "parent_cls_dict": {"自适应游戏化": "教育技术", "在线学习": "在线学习"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注自适应学习系统和游戏化对学生参与和学习表现的影响", "meta_info_content": "The impact of gamification and adaptive gamification on e-learning effectiveness was studied, particularly in terms of engagement and learning performance.", "classified": ["自适应学习系统", "游戏化", "学生参与", "学习表现"], "parent_cls_dict": {"自适应学习系统": "教育技术", "游戏化": "教育方法", "学生参与": "教育心理学", "学习表现": "教育评估"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源于学生在学习管理系统中的交互活动", "meta_info_content": "Students' needs can be determined by analyzing their navigation through e-learning systems, creating massive datasets.", "classified": ["学习管理系统"], "parent_cls_dict": {"学习管理系统": "系统数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用分类算法评估学生行为和学习类型", "meta_info_content": "Classification techniques like decision trees and naïve Bayes have been applied to predict student performance and behavior.", "classified": ["分类算法", "学生行为分析", "学习类型评估"], "parent_cls_dict": {"分类算法": "分类技术", "学生行为分析": "教育分析", "学习类型评估": "教育分析"}}, {"meta_info_type": "模型评估方法", "meta_info_desc": "使用k-nearest neighbor算法评估，准确率达到95%", "meta_info_content": "Five classifiers (decision tree, naïve Bayesian, k-nearest neighbor) were created and evaluated, with k-nearest neighbor providing the best accuracy (95%).", "classified": ["k-nearest neighbor", "准确率"], "parent_cls_dict": {"k-nearest neighbor": "机器学习", "准确率": "模型评估"}}, {"meta_info_type": "主要发现", "meta_info_desc": "自适应游戏化显著提高学生的学习参与度和表现", "meta_info_content": "Results indicated that adaptive gamification had the highest effect on student engagement compared to gamification and classical learning methods.", "classified": ["自适应游戏化", "学习参与度", "学习表现"], "parent_cls_dict": {"自适应游戏化": "教育技术", "学习参与度": "学习行为", "学习表现": "教育结果"}}, {"meta_info_type": "局限性", "meta_info_desc": "未充分考虑学生个体差异和个性化的游戏元素适配", "meta_info_content": "Most studies fail to consider individual differences in student personalities or adapt game elements accordingly.", "classified": ["个体差异", "游戏化"], "parent_cls_dict": {"个体差异": "个性化", "游戏化": "教育技术"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为第一年工程学课程的学生", "meta_info_content": "The course is part of a first-year Engineering degree offered at a public university.", "classified": ["大学生", "工程教育"], "parent_cls_dict": {"大学生": "高等教育", "工程教育": "工程教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在工程学领域，特别是电子学基础", "meta_info_content": "The course under analysis is a first-year Engineering course covering the basics of Electronics.", "classified": ["工程学", "电子学基础"], "parent_cls_dict": {"工程学": "工程", "电子学基础": "工程"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "采用翻转课堂和混合学习方法，通过LMS进行数据采集", "meta_info_content": "The flipped learning methodology shifts content delivery from traditional lectures to pre-recorded videos, quizzes, and blended learning activities accessible through the LMS.", "classified": ["翻转课堂", "混合学习环境", "学习管理系统"], "parent_cls_dict": {"翻转课堂": "学习形式", "混合学习环境": "混合学习", "学习管理系统": "学习管理"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "每年约有400名学生参与评估", "meta_info_content": "With approximately 400 students evaluated annually.", "classified": ["学生", "参与人数"], "parent_cls_dict": {"学生": "参与者", "参与人数": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在通过分析学生早期行为预测课程成功与否", "meta_info_content": "The extent to which failure at the end of the course can be anticipated is analyzed by examining students' behavior related to academic activities in the early stages of the course.", "classified": ["学生行为分析", "课程成功预测"], "parent_cls_dict": {"学生行为分析": "教育研究", "课程成功预测": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注早期行为数据与课程成功的关系", "meta_info_content": "This study analyzes whether success can be anticipated during the early stages of a course using behavioral data.", "classified": ["早期行为数据", "课程成功"], "parent_cls_dict": {"早期行为数据": "数据分析", "课程成功": "教育评估"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于学习分析和教育数据挖掘理论", "meta_info_content": "Learning analytics focuses on the measurement, collection, analysis, and reporting of data related to the learning process. EDM emphasizes techniques for extracting meaningful information from data.", "classified": ["学习分析", "教育数据挖掘"], "parent_cls_dict": {"学习分析": "数据科学", "教育数据挖掘": "数据科学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源包括在线活动记录、课堂出勤和预先存在的数据", "meta_info_content": "Data were collected from three main sources: Online Activities, On-Campus Activities, Pre-Existing Data.", "classified": ["活动记录", "出勤数据"], "parent_cls_dict": {"活动记录": "行为数据", "出勤数据": "行为数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用决策树、支持向量机和k近邻算法进行数据建模", "meta_info_content": "Feature selection and classification approaches are commonly employed in EDM, with decision trees (DT), support vector machines (SVM), and k-nearest neighbors (KNN) being popular techniques.", "classified": ["决策树", "支持向量机", "k近邻算法", "数据建模"], "parent_cls_dict": {"决策树": "分类技术", "支持向量机": "机器学习", "k近邻算法": "机器学习", "数据建模": "数据分析"}}, {"meta_info_type": "主要发现", "meta_info_desc": "早期活动数据可有效预测翻转课堂设定中的学业成功，关键因素包括讲座观看和课堂出勤", "meta_info_content": "This study demonstrates that early activity data can effectively predict academic success in a flipped classroom setting. Key factors for success include lecture watching and class attendance for first-time students.", "classified": ["早期活动数据", "学业成功预测", "讲座观看", "课堂出勤"], "parent_cls_dict": {"早期活动数据": "数据分析", "学业成功预测": "教育结果", "讲座观看": "学习行为", "课堂出勤": "学习行为"}}, {"meta_info_type": "局限性", "meta_info_desc": "由于早期阶段缺乏评分数据，模型存在局限性", "meta_info_content": "Models provide insights into factors relevant to academic success but introduce limitations due to the absence of assessments or grades during this period.", "classified": ["评分数据", "模型局限"], "parent_cls_dict": {"评分数据": "数据管理", "模型局限": "AI"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为大学生，涉及城市规划课程", "meta_info_content": "GIS-based learning experiences were analyzed in two urban planning courses at the University of Alicante: Urban Planning 1 (second year) and Urban Planning 6 (fifth year).", "classified": ["大学生", "高等教育"], "parent_cls_dict": {"大学生": "高等教育", "高等教育": "高等教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在工程和科学教育中的应用", "meta_info_content": "The aim of this study is to analyze the utilization of innovative technologies in engineering and science education.", "classified": ["工程教育", "科学教育"], "parent_cls_dict": {"工程教育": "工程", "科学教育": "科学教育"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "使用地理信息系统（GIS）作为学习工具", "meta_info_content": "This research focuses on computational Geographic Information Systems (GIS), which enable access to and management of large volumes of information and data.", "classified": ["GIS工具"], "parent_cls_dict": {"GIS工具": "学习技术"}}, {"meta_info_type": "研究目的", "meta_info_desc": "分析创新技术在工程和科学教育中的应用", "meta_info_content": "The aim of this study is to analyze the utilization of innovative technologies in engineering and science education.", "classified": ["创新技术", "工程教育", "科学教育"], "parent_cls_dict": {"创新技术": "教育技术", "工程教育": "教育", "科学教育": "教育"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注地理信息系统（GIS）在教育中的应用", "meta_info_content": "This research focuses on computational Geographic Information Systems (GIS).", "classified": ["地理信息系统", "教育应用"], "parent_cls_dict": {"地理信息系统": "教育技术", "教育应用": "教育管理"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于GIS工具的主动学习方法论", "meta_info_content": "GIS can serve as a foundation for active learning methodologies, fostering motivation and engagement.", "classified": ["GIS工具", "主动学习"], "parent_cls_dict": {"GIS工具": "技术工具", "主动学习": "学习方法"}}, {"meta_info_type": "数据来源", "meta_info_desc": "通过学生学习过程和经验中获取数据", "meta_info_content": "Examining data obtained from students’ learning processes and experiences.", "classified": ["学习过程"], "parent_cls_dict": {"学习过程": "教育活动"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "采用GIS分析动态和静态地理信息", "meta_info_content": "Analyzing dynamic geographic information to study urban growth and territory occupation. Using static geographic information to identify and explain city components.", "classified": ["GIS", "地理信息分析"], "parent_cls_dict": {"GIS": "地理信息系统", "地理信息分析": "空间分析"}}, {"meta_info_type": "Type of Output/Artifact", "meta_info_desc": "提出一系列补充教学活动的倡议", "meta_info_content": "A set of initiatives is proposed to complement teaching activities and improve the user experience in education.", "classified": ["倡议"], "parent_cls_dict": {"倡议": "倡议"}}, {"meta_info_type": "目标用户", "meta_info_desc": "面向STEM领域的学生和教育者", "meta_info_content": "GIS is particularly relevant in the fields of science, technology, engineering, and mathematics (STEM) education.", "classified": ["学生", "教育者"], "parent_cls_dict": {"学生": "学习者", "教育者": "教育相关人员"}}, {"meta_info_type": "主要发现", "meta_info_desc": "GIS在教育中具有积极影响，提升学生参与度和动机", "meta_info_content": "GIS-based methodologies offer several advantages: enhanced creativity and reasoning through flexible analysis options, high levels of student autonomy and motivation.", "classified": ["GIS影响", "学生参与度", "动机提升"], "parent_cls_dict": {"GIS影响": "教育技术", "学生参与度": "学习行为", "动机提升": "学习体验"}}, {"meta_info_type": "局限性", "meta_info_desc": "需要解决技术挑战和提高互操作性", "meta_info_content": "Future work should focus on addressing technological challenges, improving interoperability.", "classified": ["技术挑战", "互操作性"], "parent_cls_dict": {"技术挑战": "技术", "互操作性": "技术"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为参与认证的MOOC学习者", "meta_info_content": "The study involved 120 verified learners (VL) who enrolled for certification.", "classified": ["MOOC学习者", "成人教育"], "parent_cls_dict": {"MOOC学习者": "成人教育", "成人教育": "成人教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在工程教育中的计算机科学领域", "meta_info_content": "The development of web applications is an essential and critical skill for Computer Science Engineers.", "classified": ["工程教育", "计算机科学"], "parent_cls_dict": {"工程教育": "工程", "计算机科学": "计算机科学"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "学习环境为edX平台上的MOOC课程", "meta_info_content": "The University Autónoma of Madrid offers on the edX Platform the Massive Open Online Course (MOOC): “Introduction to Development of Web Applications.”", "classified": ["edX平台", "MOOC课程"], "parent_cls_dict": {"edX平台": "学习平台", "MOOC课程": "在线学习"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "84名活跃学习者参与实验", "meta_info_content": "The remaining 84 active learners (AVL) were divided into two groups.", "classified": ["活跃学习者", "参与人数"], "parent_cls_dict": {"活跃学习者": "参与者", "参与人数": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在测试edX-LIS对学习者行为的干预策略", "meta_info_content": "To test the intervention strategy on learners' behavior supported by edX-LIS.", "classified": ["学习行为", "干预策略"], "parent_cls_dict": {"学习行为": "教育研究", "干预策略": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注学习者的动机、持久性和参与度", "meta_info_content": "The results obtained from this study have demonstrated that the intervention strategy supported by the edX-LIS has a positive impact on the motivation, persistence, and engagement of the learners.", "classified": ["学习者动机", "持久性", "参与度"], "parent_cls_dict": {"学习者动机": "教育心理学", "持久性": "教育心理学", "参与度": "教育心理学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源于学习者活动日志和问卷调查", "meta_info_content": "Learner activity data is summarized into attributes representing daily interactions, including time spent, assignments completed, and forum participation.", "classified": ["活动日志", "问卷调查"], "parent_cls_dict": {"活动日志": "行为数据", "问卷调查": "调查数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用K-means聚类进行学习者分组", "meta_info_content": "Learners are grouped into clusters based on their performance using K-means clustering.", "classified": ["K-means聚类", "学习者分组"], "parent_cls_dict": {"K-means聚类": "聚类算法", "学习者分组": "学习分析"}}, {"meta_info_type": "主要发现", "meta_info_desc": "实验组学习者表现出更高的成功率和参与度", "meta_info_content": "EG learners demonstrated higher success rates (68%) compared to CG learners (56%).", "classified": ["实验组成功率", "学习参与度"], "parent_cls_dict": {"实验组成功率": "教育结果", "学习参与度": "学习行为"}}, {"meta_info_type": "局限性", "meta_info_desc": "研究未涉及非认证学习者的行为表现", "meta_info_content": "The study involved only verified learners who enrolled for certification.", "classified": ["研究范围", "学习者行为"], "parent_cls_dict": {"研究范围": "研究", "学习者行为": "教育效果"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为MOOCs课程的学习者", "meta_info_content": "Experiments were conducted on data sets from two MOOC courses: 'Mining of Massive Datasets' and 'Automata Theory.'", "classified": ["MOOC学习者", "成人教育"], "parent_cls_dict": {"MOOC学习者": "成人教育", "成人教育": "成人教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在在线开放课程（MOOCs）中的深度学习和学习分析", "meta_info_content": "The big data stored in massive open online course (MOOC) platforms have posed a challenge in the Learning Analytics field.", "classified": ["在线开放课程", "深度学习", "学习分析"], "parent_cls_dict": {"在线开放课程": "在线学习", "深度学习": "AI", "学习分析": "数据分析"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究在MOOCs平台上进行，涉及Coursera, Edx, Udacity", "meta_info_content": "MOOCs are among the distinguished innovations in distance learning, offering courses primarily based on video lessons through platforms such as Coursera, Edx, and Udacity.", "classified": ["MOOC平台", "<PERSON>ra平台", "edX平台", "Udacity平台"], "parent_cls_dict": {"MOOC平台": "在线学习", "Coursera平台": "学习平台", "edX平台": "学习平台", "Udacity平台": "学习平台"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在通过深度学习模型预测学习者的表现并改善教育过程", "meta_info_content": "This paper presents a visual analysis framework to allow... predict learner performance, a vital decision-making problem, by addressing their issues and improving the educational process.", "classified": ["深度学习", "学习表现预测"], "parent_cls_dict": {"深度学习": "AI", "学习表现预测": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注视频点击流数据和学习者的隐性行为特征", "meta_info_content": "This study aims to provide detailed clickstream behavior information of learners while watching videos as implicit features rather than explicit features.", "classified": ["视频点击流数据", "隐性行为特征"], "parent_cls_dict": {"视频点击流数据": "数据分析", "隐性行为特征": "教育心理学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源于MOOCs课程的视频点击流和测验分数", "meta_info_content": "Learners' video clickstream records and quiz scores were analyzed weekly.", "classified": ["MOOCs数据", "点击流", "测验分数"], "parent_cls_dict": {"MOOCs数据": "系统数据", "点击流": "系统数据", "测验分数": "评估数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用LSTM网络进行时序数据分析和预测", "meta_info_content": "The predictive model was constructed using LSTM networks to analyze temporal sequences of learners' weekly interaction data and predict performance.", "classified": ["LSTM网络", "时序数据分析", "预测"], "parent_cls_dict": {"LSTM网络": "机器学习", "时序数据分析": "数据分析", "预测": "数据分析"}}, {"meta_info_type": "模型评估方法", "meta_info_desc": "模型使用二元交叉熵与L2正则化进行优化", "meta_info_content": "The model utilized binary cross-entropy with L2 regularization for optimization.", "classified": ["二元交叉熵", "L2正则化"], "parent_cls_dict": {"二元交叉熵": "模型优化", "L2正则化": "模型优化"}}, {"meta_info_type": "主要发现", "meta_info_desc": "LSTM模型在预测学习者表现时优于其他模型", "meta_info_content": "The LSTM model outperformed baseline models, achieving an accuracy of 90.3% and 89% in the 'Mining of Massive Datasets' and 'Automata Theory' courses, respectively.", "classified": ["LSTM模型", "学习者表现预测"], "parent_cls_dict": {"LSTM模型": "AI", "学习者表现预测": "教育结果"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "MOOCs为成人学习者提供扩展知识的机会", "meta_info_content": "MOOCs offer adult learners opportunities to extend their knowledge beyond traditional higher education settings.", "classified": ["MOOC学习者", "成人教育"], "parent_cls_dict": {"MOOC学习者": "成人教育", "成人教育": "成人教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在教育技术与在线学习领域", "meta_info_content": "We reviewed 20 academic journals in education technology and e-learning.", "classified": ["教育技术", "在线学习"], "parent_cls_dict": {"教育技术": "教育技术", "在线学习": "在线学习"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "MOOCs提供开放访问的高质量教育资源，通过特定的基础设施进行沟通与学习", "meta_info_content": "MOOCs differ from traditional distance learning due to their open-access features, such as open licensing of course content and the use of specific infrastructures for communication and learning tools.", "classified": ["MOOC平台"], "parent_cls_dict": {"MOOC平台": "在线学习"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "分析了2009至2019年间发布的241篇MOOC相关论文", "meta_info_content": "This paper analyzes 241 articles published between 2009 and 2019.", "classified": ["MOOC论文"], "parent_cls_dict": {"MOOC论文": "出版物"}}, {"meta_info_type": "研究目的", "meta_info_desc": "系统分析MOOC的分类、学习者收益、学生中心学习方法及沟通策略", "meta_info_content": "The analysis centers around five key questions related to MOOC classification, learner benefits, approaches to student-centered learning, meeting learner needs, and effective communication and engagement strategies.", "classified": ["MOOC", "学生中心学习"], "parent_cls_dict": {"MOOC": "在线学习", "学生中心学习": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注MOOC的设计、学习形式、学习理论、教学方法、评估策略及沟通工具", "meta_info_content": "We applied cognitive mapping as a classification technique to analyze MOOC literature across six dimensions: MOOC design, learning forms, learning theories, teaching methods, assessment strategies, and communication tools.", "classified": ["MOOC设计", "学习形式", "学习理论", "教学方法", "评估策略", "沟通工具"], "parent_cls_dict": {"MOOC设计": "在线学习", "学习形式": "教育方法", "学习理论": "教育心理学", "教学方法": "教育方法", "评估策略": "教育评估", "沟通工具": "教育技术"}}, {"meta_info_type": "数据来源", "meta_info_desc": "从12个主要学术数据库、20个教育技术期刊及主要MOOC会议获取数据", "meta_info_content": "We searched 12 major academic databases, including ERIC, JSTOR, Scopus, Google Scholar, and IEEE Xplore. We reviewed 20 academic journals in education technology and e-learning. We included papers from major MOOC conferences, such as the ACM Learning at Scale (L@S) and EMOOCs.", "classified": ["学术数据库", "期刊", "MOOC会议"], "parent_cls_dict": {"学术数据库": "数据仓储", "期刊": "出版物", "MOOC会议": "会议"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "应用认知映射进行MOOC文献分类分析", "meta_info_content": "We applied cognitive mapping as a classification technique to analyze MOOC literature.", "classified": ["认知映射", "MOOC文献分类"], "parent_cls_dict": {"认知映射": "认知技术", "MOOC文献分类": "教育分析"}}, {"meta_info_type": "主要发现", "meta_info_desc": "MOOCs提供了多样化的学习机会，但需要提升教学工具、评估质量及保持开放性", "meta_info_content": "MOOCs have evolved significantly over the past decade, offering diverse learning opportunities. However, further research is needed to enhance pedagogical tools, improve assessments, and maintain openness.", "classified": ["MOOCs学习机会", "教学工具提升", "评估质量", "开放性"], "parent_cls_dict": {"MOOCs学习机会": "在线学习", "教学工具提升": "教育技术", "评估质量": "教学", "开放性": "学习体验"}}, {"meta_info_type": "局限性", "meta_info_desc": "MOOCs面临的挑战包括教学创新、评估质量及开放性问题", "meta_info_content": "Challenges remain, including: Pedagogical Innovation, Assessment Quality, Openness.", "classified": ["MOOC挑战", "教学创新", "评估质量", "开放性"], "parent_cls_dict": {"MOOC挑战": "在线学习", "教学创新": "教育技术", "评估质量": "评估", "开放性": "开放性"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为MOOC从业者", "meta_info_content": "A qualitative phenomenological study was conducted through semi-structured interviews with 14 MOOC practitioners.", "classified": ["MOOC学习者", "成人教育"], "parent_cls_dict": {"MOOC学习者": "成人教育", "成人教育": "成人教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "工程和非工程领域的MOOC课程", "meta_info_content": "Evidence shows diverse learners’ and practitioners’ problems shared among engineering and non-engineering courses.", "classified": ["工程MOOC课程", "非工程MOOC课程"], "parent_cls_dict": {"工程MOOC课程": "在线学习", "非工程MOOC课程": "在线学习"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "学习环境为MOOC平台", "meta_info_content": "The Massive Open Online Courses (MOOCs) movement has transformed education by enabling global-scale learning experiences.", "classified": ["MOOC平台"], "parent_cls_dict": {"MOOC平台": "在线学习"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "共有14名MOOC实践者参与研究", "meta_info_content": "A qualitative phenomenological study was conducted through semi-structured interviews with 14 MOOC practitioners.", "classified": ["MOOC实践者", "参与人数"], "parent_cls_dict": {"MOOC实践者": "参与者", "参与人数": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "研究MOOC从业者如何识别和支持面临困难的学习者", "meta_info_content": "This study investigates how MOOC practitioners identify and support learners facing problems, the challenges encountered, and how these practices differ across engineering and non-engineering MOOCs.", "classified": ["MOOC", "学习支持"], "parent_cls_dict": {"MOOC": "在线学习", "学习支持": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注反馈机制及学习者支持策略", "meta_info_content": "Feedback has been identified as an essential component of the learning process directly connected with learners' engagement.", "classified": ["反馈机制", "学习者支持策略"], "parent_cls_dict": {"反馈机制": "教育方法", "学习者支持策略": "教育心理学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "通过半结构化访谈收集数据", "meta_info_content": "Fourteen semi-structured interviews were conducted with practitioners from diverse roles (e.g., instructors, teaching assistants) and platforms.", "classified": ["半结构化访谈"], "parent_cls_dict": {"半结构化访谈": "调查数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用内容分析法进行数据分析", "meta_info_content": "Content analysis was applied to the interview data, using both predefined categories from the literature and emergent themes.", "classified": ["内容分析法", "数据分析"], "parent_cls_dict": {"内容分析法": "定性分析", "数据分析": "数据分析"}}, {"meta_info_type": "主要发现", "meta_info_desc": "MOOC从业者在识别和支持学习者方面面临挑战，尤其是在工程课程中", "meta_info_content": "The study revealed that while practitioners encounter similar problems across disciplines, [ENGINEER] practitioners were less likely to use learning analytics due to platform limitations.", "classified": ["MOOC从业者", "学习者支持", "工程课程挑战"], "parent_cls_dict": {"MOOC从业者": "在线学习", "学习者支持": "教育管理", "工程课程挑战": "工程"}}, {"meta_info_type": "局限性", "meta_info_desc": "平台限制和从业者技术技能不足限制了反馈策略的实施", "meta_info_content": "Feedback provision strategies are often limited by platform restrictions or practitioners’ skills in interpreting learning analytics.", "classified": ["平台限制", "技术技能不足"], "parent_cls_dict": {"平台限制": "技术", "技术技能不足": "教育"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究涵盖不同学习阶段的学生，包括本科生和K-12学生", "meta_info_content": "Additional group discussion prompts include: Which form of learning—crowdsourced or traditional—would you prefer as a researcher, undergraduate student, or K-12 student?", "classified": ["本科生", "K-12学生"], "parent_cls_dict": {"本科生": "高等教育", "K-12学生": "基础教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中于信息科学、图书馆科学、教育学和学习分析等领域", "meta_info_content": "Each panelist, coming from diverse fields of expertise—Information Science, Library Science, e-Learning, Learning Analytics, and Education—offers distinct perspectives on crowdsourcing in education.", "classified": ["信息科学", "图书馆科学", "教育学", "学习分析"], "parent_cls_dict": {"信息科学": "信息技术", "图书馆科学": "信息技术", "教育学": "教育", "学习分析": "数据分析"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究关注在线学习环境，特别是基于互联网技术的众包学习环境", "meta_info_content": "Existing Internet technologies provide unique learning opportunities for users online. These learning opportunities include 'learning as a group' or 'learning individually.' Tools that support 'learning as a group' have strengthened the novel concept of harnessing the 'wisdom of crowds' or 'collective intelligence.'", "classified": ["在线学习环境", "众包学习环境"], "parent_cls_dict": {"在线学习环境": "在线学习", "众包学习环境": "在线学习"}}, {"meta_info_type": "研究目的", "meta_info_desc": "探讨众包学习在教育中的作用及其面临的伦理与实际问题", "meta_info_content": "This panel sheds light on how to use the 'wisdom of crowds' or 'collective intelligence' to better articulate the process of learning in the context of ethical issues.", "classified": ["众包学习", "教育伦理"], "parent_cls_dict": {"众包学习": "教育技术", "教育伦理": "伦理"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "研究核心概念包括众包学习、群体智慧与集体智能", "meta_info_content": "The panel introduces the term 'Learnsourcing,' which encapsulates learning in a crowdsourced environment.", "classified": ["众包学习", "群体智慧", "集体智能"], "parent_cls_dict": {"众包学习": "教育技术", "群体智慧": "教育心理学", "集体智能": "教育心理学"}}, {"meta_info_type": "理论基础", "meta_info_desc": "以班杜拉的社会认知理论为理论基础", "meta_info_content": "This aligns with <PERSON><PERSON>’s (1977) socio-cognitive theory, which highlights how human behavior is shaped by observing, imitating, and modeling others.", "classified": ["社会认知理论"], "parent_cls_dict": {"社会认知理论": "心理学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "研究涉及在线Q&A社区和众包学习工具", "meta_info_content": "Man<PERSON> Rath’s research examines online Q&A sites related to STEM education for meaningful insights.", "classified": ["在线社区", "学习工具"], "parent_cls_dict": {"在线社区": "系统数据", "学习工具": "教育活动"}}, {"meta_info_type": "Type of Output/Artifact", "meta_info_desc": "输出包括对众包学习的定义、方法和未来方向的讨论", "meta_info_content": "This panel explores the perspectives, challenges, and future of crowdsourcing in education.", "classified": ["定义", "方法", "讨论"], "parent_cls_dict": {"定义": "概念", "方法": "方法论", "讨论": "交流"}}, {"meta_info_type": "目标用户", "meta_info_desc": "目标用户为从事教育研究的学者和学习技术开发者", "meta_info_content": "The session aims to address inherent issues and discuss how crowdsourcing can transform the education field.", "classified": ["研究人员", "教育技术开发者"], "parent_cls_dict": {"研究人员": "研究相关人员", "教育技术开发者": "技术相关人员"}}, {"meta_info_type": "主要发现", "meta_info_desc": "发现众包学习在教育中的潜力和面临的伦理问题", "meta_info_content": "The panel sheds light on how to use the 'wisdom of crowds' or 'collective intelligence' to better articulate the process of learning in the context of ethical issues.", "classified": ["众包学习", "伦理问题"], "parent_cls_dict": {"众包学习": "在线学习", "伦理问题": "伦理问题"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究涵盖从小学到高等教育的所有教育阶段", "meta_info_content": "The capture, aggregation, and analysis of student data is becoming ubiquitous at all levels of education—from primary to post-secondary.", "classified": ["小学教育", "中学教育", "高等教育"], "parent_cls_dict": {"小学教育": "基础教育", "中学教育": "中学教育", "高等教育": "高等教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中于教育数据挖掘和学习分析", "meta_info_content": "The panel is composed of four researchers who analyze and critique educational data mining practices and learning analytics initiatives.", "classified": ["教育数据挖掘", "学习分析"], "parent_cls_dict": {"教育数据挖掘": "数据分析", "学习分析": "数据分析"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究涉及学术图书馆、专业咨询、城市学校、在线研究生学习及高等教育领域", "meta_info_content": "The panel is composed of four researchers who analyze and critique educational data mining practices and learning analytics initiatives in particular micro- and macro-contexts, including academic libraries, professional advising, urban schools, graduate-level online learning, and higher education generally.", "classified": ["学术图书馆", "专业咨询", "城市学校", "在线研究生学习", "高等教育"], "parent_cls_dict": {"学术图书馆": "教育资源", "专业咨询": "教育服务", "城市学校": "教育机构", "在线研究生学习": "高等教育", "高等教育": "教育领域"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在探讨教育数据挖掘和学习分析中的社会技术和伦理问题", "meta_info_content": "The panel aims to raise awareness about sociotechnical and ethical questions surrounding EDM and LA, employing empirical, philosophical, and critical methods to discuss these issues.", "classified": ["教育数据挖掘", "学习分析", "伦理问题"], "parent_cls_dict": {"教育数据挖掘": "数据分析", "学习分析": "数据分析", "伦理问题": "伦理"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "教育数据挖掘、学习分析、信息伦理、数据隐私和数据可视化", "meta_info_content": "Key concepts include educational data mining, learning analytics, information ethics, data privacy, and data visualization.", "classified": ["教育数据挖掘", "学习分析", "信息伦理", "数据隐私", "数据可视化"], "parent_cls_dict": {"教育数据挖掘": "数据分析", "学习分析": "数据分析", "信息伦理": "数据伦理", "数据隐私": "数据伦理", "数据可视化": "数据分析"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于信息伦理、批判数据研究、文献研究和高等教育政策", "meta_info_content": "Each panelist also represents a unique conceptual background, pulling from work in information ethics and policy, critical data studies, documentation studies, and higher education policy.", "classified": ["信息伦理", "批判数据研究", "文献研究", "高等教育政策"], "parent_cls_dict": {"信息伦理": "伦理学", "批判数据研究": "数据科学", "文献研究": "研究方法", "高等教育政策": "教育学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "教育数据来源包括学生学习行为及课外生活细节", "meta_info_content": "Both EDM and LA require access to significant stores of data, some of which focus on student learning while other data capture the minutiae of student life outside the classroom.", "classified": ["学习行为", "生活细节"], "parent_cls_dict": {"学习行为": "行为数据", "生活细节": "个人数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "主要采用机器学习等自动化方法挖掘学习行为", "meta_info_content": "EDM applies automated methods (e.g., machine learning) to learner data in order to surface, describe, and predict learning behaviors.", "classified": ["机器学习", "学习行为挖掘"], "parent_cls_dict": {"机器学习": "机器学习", "学习行为挖掘": "学习分析"}}, {"meta_info_type": "Type of Output/Artifact", "meta_info_desc": "研究成果包括对EDM和LA实践的批判性分析及未来研究建议", "meta_info_content": "The panelists will present their research and lead audience members in discussion on EDM and LA, exploring issues, opportunities, and recommendations for research, practice, technological design, and policy.", "classified": ["分析", "建议"], "parent_cls_dict": {"分析": "分析", "建议": "建议"}}, {"meta_info_type": "目标用户", "meta_info_desc": "目标用户包括教育研究者、政策制定者和高等教育领域的专业人士", "meta_info_content": "The panel aims to engage audience members, including researchers and professionals, in the domain of educational data mining and learning analytics.", "classified": ["研究人员", "政策制定者", "高等教育专业人士"], "parent_cls_dict": {"研究人员": "研究相关人员", "政策制定者": "政策相关人员", "高等教育专业人士": "教育相关人员"}}, {"meta_info_type": "主要发现", "meta_info_desc": "数据分析和可视化实践存在伦理和社会问题，对学生和社区的影响复杂", "meta_info_content": "Initial findings highlight political and theoretical implications for students and communities represented via dashboards, and the ethical conflicts in data collection and usage.", "classified": ["数据分析", "可视化实践", "伦理问题", "社会影响"], "parent_cls_dict": {"数据分析": "数据分析", "可视化实践": "数据分析", "伦理问题": "伦理问题", "社会影响": "社会影响"}}, {"meta_info_type": "局限性", "meta_info_desc": "研究揭示数据隐私教育的不足以及对数据客观性的批判性理解有限", "meta_info_content": "Studies reveal gaps in knowledge, an overfocus on legal aspects of privacy, and limited conceptual understanding of data privacy issues.", "classified": ["数据隐私教育", "批判性理解"], "parent_cls_dict": {"数据隐私教育": "隐私", "批判性理解": "教育"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为高等教育阶段的全职教师", "meta_info_content": "The survey was administered to participants who self-identified as full-time instructors in the United States from non-profit colleges and universities.", "classified": ["高等教育教师"], "parent_cls_dict": {"高等教育教师": "高等教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在学生隐私和学习分析领域", "meta_info_content": "The study focuses on student privacy issues and their intersection with learning analytics practices.", "classified": ["学生隐私", "学习分析"], "parent_cls_dict": {"学生隐私": "教育", "学习分析": "数据分析"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究涵盖了面对面、在线和混合课程的教学环境", "meta_info_content": "Respondents represented both public and private colleges and universities and taught in face-to-face, online, and hybrid courses at both undergraduate and graduate levels.", "classified": ["面对面教学环境", "在线课程", "混合课程"], "parent_cls_dict": {"面对面教学环境": "线下学习", "在线课程": "在线学习", "混合课程": "混合学习"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "共有502名高等教育教师参与调查", "meta_info_content": "The survey captured responses from 502 instructors representing colleges and universities in the United States.", "classified": ["高等教育教师", "参与人数"], "parent_cls_dict": {"高等教育教师": "参与者", "参与人数": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在调查教师如何理解和应对学生隐私问题", "meta_info_content": "This study aims to understand how faculty perceive student privacy issues and strategize to address them in practice.", "classified": ["教师", "隐私问题"], "parent_cls_dict": {"教师": "教育", "隐私问题": "伦理"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注学生隐私、学习分析及其在教学中的应用", "meta_info_content": "Key concepts include student privacy, learning analytics, and faculty strategies for addressing privacy issues in the classroom.", "classified": ["学生隐私", "学习分析", "教学应用"], "parent_cls_dict": {"学生隐私": "数据伦理", "学习分析": "数据分析", "教学应用": "教育管理"}}, {"meta_info_type": "理论基础", "meta_info_desc": "研究基于学习分析相关文献及学生隐私权问题的伦理讨论", "meta_info_content": "The study builds on literature regarding learning analytics ethics, student privacy policies, and the role of faculty in privacy protection.", "classified": ["学习分析", "学生隐私", "伦理"], "parent_cls_dict": {"学习分析": "数据科学", "学生隐私": "伦理学", "伦理": "伦理学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源于对高等教育教师的在线问卷调查", "meta_info_content": "The survey instrument consisted of 62 questions and was administered through Qualtrics Panels to full-time instructors in the United States.", "classified": ["问卷调查"], "parent_cls_dict": {"问卷调查": "调查数据"}}, {"meta_info_type": "数据粒度", "meta_info_desc": "以教师个体为分析单位", "meta_info_content": "Each survey response corresponds to an individual instructor's perspectives and practices.", "classified": ["教师个体"], "parent_cls_dict": {"教师个体": "个体"}}, {"meta_info_type": "主要发现", "meta_info_desc": "大多数教师认为学生隐私重要，但许多人缺乏必要的知识和资源", "meta_info_content": "Survey results indicated that student privacy was very important (74.9%) or important (23.9%) to respondents, but only 64.7% felt knowledgeable enough to protect it, and 56.2% agreed they had the necessary resources.", "classified": ["学生隐私", "知识和资源缺乏"], "parent_cls_dict": {"学生隐私": "伦理问题", "知识和资源缺乏": "教育管理"}}, {"meta_info_type": "局限性", "meta_info_desc": "教师对隐私问题的知识和资源不足，可能影响隐私教育的实施", "meta_info_content": "While instructors feel responsible for protecting student privacy, many recognize their lack of knowledge and resources to do so effectively, which may explain why they rarely educate students on privacy issues.", "classified": ["隐私知识不足", "资源不足"], "parent_cls_dict": {"隐私知识不足": "隐私", "资源不足": "资源管理"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为高等教育阶段的学生", "meta_info_content": "This study provides a comprehensive review of the phenomenon of students dropping out from tertiary education.", "classified": ["高等教育"], "parent_cls_dict": {"高等教育": "高等教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在高等教育的学生辍学现象及其相关因素", "meta_info_content": "Keywords: Student dropout, higher education, dropout prediction, educational data mining, review.", "classified": ["高等教育", "学生辍学"], "parent_cls_dict": {"高等教育": "教育", "学生辍学": "教育"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究背景涉及欧洲国家高等教育系统，重点关注通过博洛尼亚进程改革后的教育结构", "meta_info_content": "The review focuses on the tertiary educational systems of European countries following the Bologna Process reforms of 1999.", "classified": ["欧洲高等教育", "博洛尼亚进程"], "parent_cls_dict": {"欧洲高等教育": "教育领域", "博洛尼亚进程": "教育政策"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在系统性分析影响学生辍学的多种层面因素，并探索现代数据挖掘技术的应用以减轻辍学率", "meta_info_content": "The findings aim to support universities in implementing early warning systems to assist students at risk.", "classified": ["学生辍学", "数据挖掘"], "parent_cls_dict": {"学生辍学": "教育研究", "数据挖掘": "数据分析"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注学生辍学的多维度影响因素，包括国家、机构和个人层面的决定因素", "meta_info_content": "The review categorizes dropout determinants into factors associated with the national education system, higher education institutions, and individual-level factors.", "classified": ["学生辍学", "多维度影响因素"], "parent_cls_dict": {"学生辍学": "教育管理", "多维度影响因素": "教育管理"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于社会学、心理学和经济学模型，同时结合综合阶段模型对学生辍学行为进行理论分析", "meta_info_content": "Theories on student dropout and retention can be grouped into sociological, psychological, and economic frameworks, and <PERSON><PERSON><PERSON> (2014) proposed a comprehensive phase model.", "classified": ["社会学", "心理学", "经济学", "综合阶段模型", "学生辍学"], "parent_cls_dict": {"社会学": "社会科学", "心理学": "心理学", "经济学": "社会科学", "综合阶段模型": "理论模型", "学生辍学": "教育学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "提到德国国家教育面板研究（NEPS）作为数据的潜在来源", "meta_info_content": "National Educational Panel Study (NEPS) data in Germany provides an exemplary model for such research.", "classified": ["NEPS"], "parent_cls_dict": {"NEPS": "研究数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "建议使用决策树、随机森林和神经网络等现代数据挖掘方法来分析复杂因素", "meta_info_content": "Modern data mining techniques, such as decision trees, random forests, and neural networks, offer promising tools for analyzing complex interactions.", "classified": ["决策树", "随机森林", "神经网络", "数据挖掘"], "parent_cls_dict": {"决策树": "分类技术", "随机森林": "机器学习", "神经网络": "机器学习", "数据挖掘": "数据分析"}}, {"meta_info_type": "Type of Output/Artifact", "meta_info_desc": "研究输出包括早期预警系统和精准预测模型，旨在减少辍学率", "meta_info_content": "The findings aim to support universities in implementing early warning systems to assist students at risk.", "classified": ["系统", "模型"], "parent_cls_dict": {"系统": "技术工具", "模型": "技术工具"}}, {"meta_info_type": "目标用户", "meta_info_desc": "目标用户为高等教育机构及其管理者，以便采取措施降低学生辍学率", "meta_info_content": "By leveraging modern data mining techniques and comprehensive datasets, researchers can develop actionable insights to support universities.", "classified": ["高等教育机构", "管理员"], "parent_cls_dict": {"高等教育机构": "教育相关机构", "管理员": "管理相关人员"}}, {"meta_info_type": "主要发现", "meta_info_desc": "辍学现象复杂且多维度，需综合社会、心理及经济视角进行分析", "meta_info_content": "This literature review highlights the complexity of the dropout phenomenon in higher education, emphasizing the need for multi-dimensional analyses.", "classified": ["辍学现象", "社会心理经济分析"], "parent_cls_dict": {"辍学现象": "教育结果", "社会心理经济分析": "社会分析"}}, {"meta_info_type": "局限性", "meta_info_desc": "许多研究局限于小型数据集或单一机构，忽视了软性因素如动机和满意度", "meta_info_content": "Most research relies on small datasets or single-institution studies, limiting the generalizability of findings.", "classified": ["数据集局限", "机构局限", "软性因素"], "parent_cls_dict": {"数据集局限": "数据管理", "机构局限": "教育", "软性因素": "心理学"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为初中阶段学生", "meta_info_content": "This design‐based, multiple‐case study examined two middle-school teachers—Ms<PERSON> and Mr. <PERSON>—who implemented an argument‐based inquiry approach.", "classified": ["初中生"], "parent_cls_dict": {"初中生": "中学教育"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "采用了Science Talk Writing Heuristic (STWH)作为教学方法", "meta_info_content": "The STWH integrates talk and writing to engage students in argumentation throughout inquiry activities.", "classified": ["STWH教学方法"], "parent_cls_dict": {"STWH教学方法": "教学法"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "研究分析了24段课堂录像", "meta_info_content": "Data sources included 24 videotaped classroom observations, field notes, student work, and artifacts.", "classified": ["课堂录像"], "parent_cls_dict": {"课堂录像": "数据"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在探讨教师如何通过学生的论证理解来管理科学论证中的不确定性", "meta_info_content": "This study investigates how teachers manage uncertainty by using students’ epistemic understanding of argument as a resource within an argumentative environment.", "classified": ["教师", "科学论证"], "parent_cls_dict": {"教师": "教育", "科学论证": "教育"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注科学论证中的不确定性和社会协商过程", "meta_info_content": "Key concepts include managing uncertainty in argumentation and fostering social negotiation.", "classified": ["科学论证", "社会协商过程"], "parent_cls_dict": {"科学论证": "教育方法", "社会协商过程": "教育心理学"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于科学实践中的不确定性处理和社会协商理论", "meta_info_content": "The study draws on theories related to uncertainty in scientific practices and social negotiation in argumentation.", "classified": ["科学实践", "不确定性处理", "社会协商理论"], "parent_cls_dict": {"科学实践": "科学哲学", "不确定性处理": "科学哲学", "社会协商理论": "社会科学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源包括课堂录像、现场笔记、学生作品和教学材料", "meta_info_content": "Data sources included 24 videotaped classroom observations, field notes, student work, and artifacts.", "classified": ["课堂录像", "现场笔记", "学生作品", "教学材料"], "parent_cls_dict": {"课堂录像": "教育活动", "现场笔记": "调查数据", "学生作品": "教育活动", "教学材料": "教育活动"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "采用恒定比较法分析教学过程中的不确定性管理", "meta_info_content": "Using the constant comparative method, the study analyzed how each teacher managed uncertainty across three stages—raising, maintaining, and reducing.", "classified": ["恒定比较法", "教学过程分析", "不确定性管理"], "parent_cls_dict": {"恒定比较法": "定性分析", "教学过程分析": "教育分析", "不确定性管理": "教育分析"}}, {"meta_info_type": "主要发现", "meta_info_desc": "发现有效的不确定性管理包括提出、维持和减少不确定性，促进了学生科学知识的协作发展", "meta_info_content": "Results showed that uncertainty in argumentation created productive moments for students to collaborate in dialogue and navigate their understanding of natural phenomena toward more coherent scientific explanations.", "classified": ["不确定性管理", "科学知识协作"], "parent_cls_dict": {"不确定性管理": "教学", "科学知识协作": "学术合作"}}, {"meta_info_type": "局限性", "meta_info_desc": "研究仅针对两位教师的课堂案例，样本范围有限", "meta_info_content": "The study examined two middle-school teachers, limiting the scope of generalizability.", "classified": ["样本范围", "课堂案例"], "parent_cls_dict": {"样本范围": "研究", "课堂案例": "教育"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为高中生，年龄范围为14-17岁", "meta_info_content": "Data were collected from 142 students (grades 10–12, ages 14–17) in southern Michigan and 133 students in southern Finland.", "classified": ["高中生"], "parent_cls_dict": {"高中生": "中学教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在科学教育领域，特别是科学实践与学生参与度的关联", "meta_info_content": "This study seeks to understand how different scientific practices in high school science classrooms are associated with students' situational engagement.", "classified": ["科学教育", "科学实践", "学生参与度"], "parent_cls_dict": {"科学教育": "科学教育", "科学实践": "科学教育", "学生参与度": "教育"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究在课堂环境中进行，使用智能手机进行数据采集", "meta_info_content": "Students were prompted via smartphones three times per lesson to answer questions about their engagement and the scientific practices they were performing.", "classified": ["课堂环境", "智能手机"], "parent_cls_dict": {"课堂环境": "线下学习", "智能手机": "技术工具"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "研究涉及275名学生，分别来自美国和芬兰", "meta_info_content": "Data were collected from 142 students in southern Michigan and 133 students in southern Finland.", "classified": ["学生", "参与人数"], "parent_cls_dict": {"学生": "参与者", "参与人数": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "探讨科学实践如何在高中科学课堂中促进学生的情境参与", "meta_info_content": "This study investigates how specific classroom activities related to scientific practices can trigger and sustain engagement in real-time high school science settings.", "classified": ["科学实践", "高中教育"], "parent_cls_dict": {"科学实践": "教育研究", "高中教育": "教育"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "情境参与作为关键变量，与科学实践的关联性受到关注", "meta_info_content": "Situational engagement is conceptualized as the balance between skills, interest, and challenge, measured when all these components are reported as high.", "classified": ["情境参与", "科学实践"], "parent_cls_dict": {"情境参与": "教育心理学", "科学实践": "教育方法"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于情境参与理论和科学实践框架", "meta_info_content": "Situational engagement is a micro-level phenomenon influenced by classroom activities, where students' skills match the challenge of the task, and they express interest in the activity.", "classified": ["情境参与理论", "科学实践框架"], "parent_cls_dict": {"情境参与理论": "学习理论", "科学实践框架": "科学哲学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据通过体验采样法（ESM）从学生处收集", "meta_info_content": "Data were collected using the experience sampling method (ESM) from 142 students in southern Michigan (993 ESM responses) and 133 students in southern Finland (1,351 ESM responses).", "classified": ["体验采样法"], "parent_cls_dict": {"体验采样法": "调查数据"}}, {"meta_info_type": "数据粒度", "meta_info_desc": "以学生每节课的多次反馈为分析单位", "meta_info_content": "Students were prompted via smartphones three times per lesson to answer questions about their engagement and the scientific practices they were performing.", "classified": ["学生反馈"], "parent_cls_dict": {"学生反馈": "反馈"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用描述性统计和逻辑回归分析科学实践与情境参与的关系", "meta_info_content": "Logistic regression models showed that scientific practices related to modeling were strongly associated with situational engagement.", "classified": ["描述性统计", "逻辑回归", "科学实践分析", "情境参与分析"], "parent_cls_dict": {"描述性统计": "统计分析", "逻辑回归": "预测技术", "科学实践分析": "教育分析", "情境参与分析": "教育分析"}}, {"meta_info_type": "主要发现", "meta_info_desc": "科学实践中的建模显著促进了学生的情境参与", "meta_info_content": "Scientific practices related to developing models and constructing explanations were associated with higher student situational engagement than other practices.", "classified": ["科学实践建模", "情境参与"], "parent_cls_dict": {"科学实践建模": "科学实践", "情境参与": "学习行为"}}, {"meta_info_type": "局限性", "meta_info_desc": "样本范围有限，数据为自我报告且科学实践有重叠现象", "meta_info_content": "The study's findings are based on a limited number of classrooms in two countries and may not represent broader contexts. Students' understanding of scientific practices may vary, and their reports were not independently verified. Scientific practices often occurred simultaneously, complicating the analysis.", "classified": ["样本范围", "自我报告", "科学实践"], "parent_cls_dict": {"样本范围": "研究", "自我报告": "数据管理", "科学实践": "科学"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为高等教育阶段学生", "meta_info_content": "The paper describes a practical experience in a gamification project involving approximately 4,000 students.", "classified": ["高等教育"], "parent_cls_dict": {"高等教育": "高等教育"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究涉及多个在线学习平台，包括Moodle、edX和Sakai", "meta_info_content": "Key tests included verifying interoperability with multiple e-learning platforms (Moodle, edX, Sakai).", "classified": ["<PERSON><PERSON><PERSON>平台", "edX平台", "Sakai平台"], "parent_cls_dict": {"Moodle平台": "学习平台", "edX平台": "学习平台", "Sakai平台": "学习平台"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "项目涉及3,835名学生", "meta_info_content": "UPCTforma was validated through a real-world gamification project involving 3,835 students.", "classified": ["学生", "参与人数"], "parent_cls_dict": {"学生": "参与者", "参与人数": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在通过UPCTforma工具提升内容互操作性、学习分析和学习动机", "meta_info_content": "This paper presents the architecture of UPCTforma, focusing on its support for interoperability in content linking, learning analytics, and motivation.", "classified": ["学习工具", "学习分析", "学习动机"], "parent_cls_dict": {"学习工具": "教育技术", "学习分析": "数据分析", "学习动机": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注互操作性、学习分析和学习动机", "meta_info_content": "Leveraging Learning Tools Interoperability (LTI) and Caliper specifications, the tool enhances content production in three key areas: tool interoperability, learning analytics, and learner motivation.", "classified": ["互操作性", "学习分析", "学习动机"], "parent_cls_dict": {"互操作性": "教育技术", "学习分析": "数据分析", "学习动机": "教育心理学"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于LTI和Caliper标准实现互操作性和学习分析", "meta_info_content": "Learning Tools Interoperability (LTI) enables content and applications to be linked from multiple platforms, while standards like Caliper focus on learning analytics interoperability by defining formats for tracking data.", "classified": ["LTI标准", "Caliper标准", "互操作性", "学习分析"], "parent_cls_dict": {"LTI标准": "技术标准", "Caliper标准": "技术标准", "互操作性": "技术工具", "学习分析": "数据科学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "通过学生活动生成的跟踪数据", "meta_info_content": "The architecture transforms tracking data into 'learning analysis models' for higher-level abstraction, enabling activity-specific analytics.", "classified": ["跟踪数据"], "parent_cls_dict": {"跟踪数据": "系统数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用Caliper事件数据进行学习分析和动机建模", "meta_info_content": "The Caliper Framework defines a common language for labeling, capturing, and presenting tracking data generated during learning interactions.", "classified": ["Caliper事件数据", "学习分析", "动机建模"], "parent_cls_dict": {"Caliper事件数据": "数据处理", "学习分析": "学习分析", "动机建模": "教育分析"}}, {"meta_info_type": "模型评估方法", "meta_info_desc": "通过模拟和压力测试评估，包括处理高达1,000名同时在线用户", "meta_info_content": "A simulator was developed to test UPCTforma under various scenarios, including scalability through stress testing with up to 1,000 simultaneous players.", "classified": ["模拟", "压力测试"], "parent_cls_dict": {"模拟": "测试方法", "压力测试": "测试方法"}}, {"meta_info_type": "Type of Output/Artifact", "meta_info_desc": "产出为支持互操作性、学习分析和动机增强的架构和工具", "meta_info_content": "UPCTforma's architecture consists of highly decoupled components including Interoperability, Tracking, Event Analyzer, and Motivation Components.", "classified": ["架构", "工具"], "parent_cls_dict": {"架构": "技术架构", "工具": "技术工具"}}, {"meta_info_type": "目标用户", "meta_info_desc": "目标用户包括教师、学生和教育机构", "meta_info_content": "Teachers manage credentials and connection histories; students access their activities; administrators can manage teacher permissions and game resources.", "classified": ["教育者", "学生", "教育机构"], "parent_cls_dict": {"教育者": "教育相关人员", "学生": "学习者", "教育机构": "教育相关机构"}}, {"meta_info_type": "主要发现", "meta_info_desc": "UPCTforma成功支持多平台互操作性、实时学习分析和动机增强", "meta_info_content": "The architecture successfully met interoperability, learning analytics, motivation, and scalability requirements. Key findings include the use of LTI for seamless integration and learning analytics for valuable insights.", "classified": ["UPCTforma", "平台互操作性", "学习分析", "动机增强"], "parent_cls_dict": {"UPCTforma": "教育技术", "平台互操作性": "教育技术", "学习分析": "数据分析", "动机增强": "学习体验"}}, {"meta_info_type": "局限性", "meta_info_desc": "未来工作包括扩展支持更多标准和自动化感知器创建", "meta_info_content": "Future work aims to develop domain-specific languages for activity models and motivation rules, and extend support to additional standards like xAPI and cmi5.", "classified": ["标准扩展", "自动化感知器"], "parent_cls_dict": {"标准扩展": "技术", "自动化感知器": "技术"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为各类教育情境下的学生", "meta_info_content": "The data that drive EDM come from a variety of sources, including intelligent tutoring systems, MOOCs, educational games, simulations, and classroom technologies.", "classified": ["不确定教育阶段"], "parent_cls_dict": {"不确定教育阶段": "不确定教育阶段"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究涉及教育数据挖掘，涵盖认知科学、计算机科学、心理学和教育等领域", "meta_info_content": "EDM is an exciting and rapidly growing area that combines multiple disciplines including cognitive science, computer science, cognitive psychology, education, and statistics.", "classified": ["教育数据挖掘", "认知科学", "计算机科学", "心理学", "教育"], "parent_cls_dict": {"教育数据挖掘": "数据分析", "认知科学": "科学", "计算机科学": "计算机科学", "心理学": "科学", "教育": "教育"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "数据来源包括智能辅导系统、大规模开放在线课程（MOOCs）、教育游戏、模拟和课堂技术", "meta_info_content": "The data that drive EDM come from a variety of sources, including intelligent tutoring systems, MOOCs, educational games, simulations, and classroom technologies.", "classified": ["智能辅导系统", "MOOC平台", "教育游戏", "模拟", "课堂技术"], "parent_cls_dict": {"智能辅导系统": "学习技术", "MOOC平台": "在线学习", "教育游戏": "学习工具", "模拟": "学习技术", "课堂技术": "学习技术"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在通过数据分析理解学生学习过程并改进学习支持系统", "meta_info_content": "EDM seeks to understand how students learn and to create better support for learning using data analysis.", "classified": ["数据分析", "学习支持"], "parent_cls_dict": {"数据分析": "数据分析", "学习支持": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "核心概念包括认知、元认知、动机、情感、语言和协作学习", "meta_info_content": "EDM researchers are addressing questions of cognition, metacognition, motivation, affect, language, social discourse, and more.", "classified": ["认知", "元认知", "动机", "情感", "语言", "协作学习"], "parent_cls_dict": {"认知": "教育心理学", "元认知": "教育心理学", "动机": "教育心理学", "情感": "教育心理学", "语言": "语言学习", "协作学习": "教育方法"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于认知科学、心理学和机器学习模型等理论", "meta_info_content": "Participating disciplines include cognitive science, computer science (human-computer interaction, machine learning, artificial intelligence), cognitive psychology, education (psychometrics, educational psychology, learning sciences), and statistics.", "classified": ["认知科学", "心理学", "机器学习"], "parent_cls_dict": {"认知科学": "科学研究", "心理学": "心理学", "机器学习": "人工智能"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源包括学生交互日志、讨论帖、传感器数据等", "meta_info_content": "The data include detailed logs of student interactions, such as graded responses, steps in problem-solving, discussion forum posts, or chat dialogs. External sensors such as eye tracking, facial expression, and body movement are also employed.", "classified": ["交互日志", "讨论帖", "传感器数据"], "parent_cls_dict": {"交互日志": "系统数据", "讨论帖": "行为数据", "传感器数据": "系统数据"}}, {"meta_info_type": "数据粒度", "meta_info_desc": "数据粒度为学生的知识组件、情感状态和语言交互行为等", "meta_info_content": "Aspects of human intelligence can collectively be referred to as a 'knowledge base,' with elements referred to as 'knowledge components' (KCs).", "classified": ["知识组件", "情感状态", "语言交互行为"], "parent_cls_dict": {"知识组件": "知识", "情感状态": "情感", "语言交互行为": "行为"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用贝叶斯知识追踪、逻辑回归、隐马尔可夫模型等分析学生学习", "meta_info_content": "EDM research has developed statistical models such as Bayesian Knowledge Tracing (BKT), logistic regression-based models, and techniques like Hidden Markov Models (HMMs) to predict student responses.", "classified": ["贝叶斯知识追踪", "逻辑回归", "隐马尔可夫模型", "学生学习分析"], "parent_cls_dict": {"贝叶斯知识追踪": "预测技术", "逻辑回归": "预测技术", "隐马尔可夫模型": "机器学习", "学生学习分析": "学习分析"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "采用监督学习方法动态建模情感和动机", "meta_info_content": "EDM researchers use supervised learning methods to model constructs like affect, motivation, and metacognition dynamically."}, {"meta_info_type": "模型评估方法", "meta_info_desc": "通过竞赛和实验评估预测模型的精确性", "meta_info_content": "Competitions such as the 2010 KDD Cup have compared statistical models for predicting student responses to problem-solving tasks.", "classified": ["竞赛", "实验"], "parent_cls_dict": {"竞赛": "研究方法", "实验": "研究方法"}}, {"meta_info_type": "Type of Output/Artifact", "meta_info_desc": "产出包括认知模型、自动化评分和协作分析系统", "meta_info_content": "Examples include using statistical assessment methods to improve predictions of student responses, discovering better cognitive models, and applying machine learning to produce automated agents supporting collaborative learning.", "classified": ["模型", "系统"], "parent_cls_dict": {"模型": "技术工具", "系统": "技术工具"}}, {"meta_info_type": "目标用户", "meta_info_desc": "主要目标用户为教育技术的设计者、研究者和教育工作者", "meta_info_content": "The findings are aimed at improving educational technologies and instructional designs for educators and researchers.", "classified": ["教育技术设计者", "研究人员", "教育者"], "parent_cls_dict": {"教育技术设计者": "技术相关人员", "研究人员": "研究相关人员", "教育者": "教育相关人员"}}, {"meta_info_type": "主要发现", "meta_info_desc": "数据驱动的认知模型比人工生成模型更有效", "meta_info_content": "Data-driven cognitive models often outperform human-generated models in accuracy and instructional design effectiveness.", "classified": ["数据驱动认知模型", "人工生成模型"], "parent_cls_dict": {"数据驱动认知模型": "数据分析", "人工生成模型": "AI"}}, {"meta_info_type": "局限性", "meta_info_desc": "对多技能评估和模型普适性的研究尚不足", "meta_info_content": "Future work should explore multi-skill assessment approaches and test the generalizability of cognitive models.", "classified": ["多技能评估", "模型普适性"], "parent_cls_dict": {"多技能评估": "评估", "模型普适性": "AI"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究涵盖传统课堂和计算机基础系统中的学生", "meta_info_content": "Gathering data from various educational environments, such as traditional classrooms and computer-based systems.", "classified": ["不确定教育阶段"], "parent_cls_dict": {"不确定教育阶段": "不确定教育阶段"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究涉及教育数据挖掘领域，结合计算机科学、教育学和统计学", "meta_info_content": "The field of EDM intersects computer science, education, and statistics.", "classified": ["教育数据挖掘", "计算机科学", "教育学", "统计学"], "parent_cls_dict": {"教育数据挖掘": "数据分析", "计算机科学": "计算机科学", "教育学": "教育", "统计学": "科学"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "包括传统课堂、远程学习平台和讨论论坛", "meta_info_content": "Educational environments include traditional classrooms, distance learning classes, and discussion forums.", "classified": ["传统课堂", "远程学习平台", "讨论论坛"], "parent_cls_dict": {"传统课堂": "线下学习", "远程学习平台": "在线学习", "讨论论坛": "学习工具"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在通过分析教育数据改善学习成果，并解释教育现象", "meta_info_content": "The aim is to better understand how students learn, improve educational outcomes, and explain educational phenomena.", "classified": ["教育数据", "学习成果"], "parent_cls_dict": {"教育数据": "数据分析", "学习成果": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注教育数据挖掘中模式发现、预测模型与个性化学习", "meta_info_content": "Key concepts include pattern discovery, predictive models, and personalized learning environments.", "classified": ["模式发现", "预测模型", "个性化学习"], "parent_cls_dict": {"模式发现": "数据分析", "预测模型": "数据分析", "个性化学习": "教育技术"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于智能辅导系统、人工智能教育和技术增强学习领域的研究", "meta_info_content": "EDM originated from research in intelligent tutoring systems, artificial intelligence in education, and technology-enhanced learning.", "classified": ["智能辅导系统", "人工智能教育", "技术增强学习"], "parent_cls_dict": {"智能辅导系统": "教育技术", "人工智能教育": "教育技术", "技术增强学习": "教育技术"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源包括学生互动、协作活动、行政记录、人口统计数据及情感信息", "meta_info_content": "EDM involves a variety of data types, including student interactions, collaborative activities, administrative records, demographic data, and affective information.", "classified": ["互动", "协作活动", "行政记录", "人口统计", "情感信息"], "parent_cls_dict": {"互动": "行为数据", "协作活动": "教育活动", "行政记录": "行政数据", "人口统计": "人口数据", "情感信息": "个人数据"}}, {"meta_info_type": "数据粒度", "meta_info_desc": "数据粒度涉及层次结构和保密性问题", "meta_info_content": "Addressing issues of granularity, hierarchy, and confidentiality.", "classified": ["层次结构", "保密性"], "parent_cls_dict": {"层次结构": "结构", "保密性": "隐私"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用分类、聚类、关系挖掘、社会网络分析等技术", "meta_info_content": "Techniques include classification, clustering, relationship mining, social network analysis, and process mining.", "classified": ["分类", "聚类", "关系挖掘", "社交网络分析"], "parent_cls_dict": {"分类": "分类技术", "聚类": "聚类算法", "关系挖掘": "数据挖掘", "社交网络分析": "网络分析"}}, {"meta_info_type": "模型评估方法", "meta_info_desc": "通过可视化技术和推荐系统解释结果以改进教育系统", "meta_info_content": "Making the derived knowledge actionable for improving educational environments or systems, often through visualization techniques or recommender systems.", "classified": ["可视化技术", "推荐系统"], "parent_cls_dict": {"可视化技术": "数据分析", "推荐系统": "推荐系统"}}, {"meta_info_type": "Type of Output/Artifact", "meta_info_desc": "产出包括预测学生表现、个性化学习环境和教育资源推荐", "meta_info_content": "Applications include predicting student performance, personalizing learning environments, and recommending activities and resources.", "classified": ["预测", "环境", "推荐"], "parent_cls_dict": {"预测": "方法论", "环境": "学习环境", "推荐": "推荐系统"}}, {"meta_info_type": "目标用户", "meta_info_desc": "目标用户为教育工作者、机构和学生", "meta_info_content": "EMD aims to support instructors, institutions, and students by providing actionable insights.", "classified": ["教育者", "教育机构", "学生"], "parent_cls_dict": {"教育者": "教育相关人员", "教育机构": "教育相关机构", "学生": "学习者"}}, {"meta_info_type": "主要发现", "meta_info_desc": "发现EDM能够改善学习成果并为教育决策提供依据", "meta_info_content": "EDM applies DM techniques to analyze educational data, test learning theories, and inform decision-making.", "classified": ["EDM应用", "学习成果改善", "教育决策"], "parent_cls_dict": {"EDM应用": "数据分析", "学习成果改善": "教育结果", "教育决策": "教育管理"}}, {"meta_info_type": "局限性", "meta_info_desc": "需要标准化数据格式并推广复制研究以提高结果普适性", "meta_info_content": "Future work should focus on standardizing data formats and promoting replication studies to generalize findings across contexts.", "classified": ["数据格式标准化", "复制研究"], "parent_cls_dict": {"数据格式标准化": "数据管理", "复制研究": "研究"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究对象为高等教育阶段的MOOC学习者", "meta_info_content": "MOOCs offer education beyond the confines of individual universities and organizations, with the possibility of free participation for large numbers of learners from any geographical location without formal entry requirements.", "classified": ["MOOC学习者", "高等教育"], "parent_cls_dict": {"MOOC学习者": "成人教育", "高等教育": "高等教育"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究聚焦于MOOC平台，如Coursera、Udacity、edX", "meta_info_content": "xMOOCs are unidirectional and typically structured around conventional lecture formats, delivered through proprietary platforms like Coursera, Udacity, and edX.", "classified": ["MOOC平台", "<PERSON>ra平台", "Udacity平台", "edX平台"], "parent_cls_dict": {"MOOC平台": "在线学习", "Coursera平台": "学习平台", "Udacity平台": "学习平台", "edX平台": "学习平台"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在综述教育数据科学在MOOC中的应用", "meta_info_content": "This paper aims to provide a comprehensive review of the existing literature to help us understand the application of EDS in MOOCs.", "classified": ["教育数据科学", "MOOC"], "parent_cls_dict": {"教育数据科学": "数据分析", "MOOC": "在线学习"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注教育数据科学（EDS）在MOOC中的应用", "meta_info_content": "MOOCs provide a wealth of information about how large numbers of learners interact with educational platforms and engage with the courses offered. The extensive amount of data provided by MOOCs concerning students’ usage information is a gold mine for EDS.", "classified": ["教育数据科学", "MOOC"], "parent_cls_dict": {"教育数据科学": "数据分析", "MOOC": "在线学习"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于学习分析、教育数据挖掘、机器学习等领域的方法", "meta_info_content": "Educational Data Science (EDS) is the application of data science (DS) to educational data. It involves computational statistics, data mining, machine learning, natural language processing (NLP), and human-computer interaction.", "classified": ["学习分析", "教育数据挖掘", "机器学习"], "parent_cls_dict": {"学习分析": "数据科学", "教育数据挖掘": "数据科学", "机器学习": "人工智能"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源于MOOC学习者的交互数据，如使用模式、视频观看行为、论坛参与等", "meta_info_content": "The interaction data from MOOCs, such as usage patterns, video viewing behavior, and forum participation, can provide valuable insights for instructors and course designers.", "classified": ["MOOC数据", "交互数据"], "parent_cls_dict": {"MOOC数据": "系统数据", "交互数据": "系统数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用社交网络分析、聚类算法、逻辑回归、决策树等技术", "meta_info_content": "Techniques like social network analysis (SNA) and clustering algorithms have been used to identify engagement patterns and improve course designs. Predictive models, such as logistic regression and decision trees, have been applied to identify at-risk students early in the course.", "classified": ["社交网络分析", "聚类算法", "逻辑回归", "决策树"], "parent_cls_dict": {"社交网络分析": "网络分析", "聚类算法": "聚类算法", "逻辑回归": "预测技术", "决策树": "分类技术"}}, {"meta_info_type": "Type of Output/Artifact", "meta_info_desc": "提供可改进MOOC课程设计和学习体验的分析工具和方法", "meta_info_content": "Automated grading systems using machine learning and peer-assessment methods have been tested to address this issue. Tools like Codewebs and Overcode help instructors provide semi-automated feedback to students.", "classified": ["工具", "方法"], "parent_cls_dict": {"工具": "技术工具", "方法": "方法论"}}, {"meta_info_type": "目标用户", "meta_info_desc": "MOOC课程设计者和教育研究者", "meta_info_content": "The interaction data from MOOCs can provide valuable insights for instructors and course designers.", "classified": ["课程设计者", "研究人员"], "parent_cls_dict": {"课程设计者": "技术相关人员", "研究人员": "研究相关人员"}}, {"meta_info_type": "主要发现", "meta_info_desc": "MOOCs为EDS的应用提供了独特机会，特别是在交互分析、预测辍学率和适应性学习方面", "meta_info_content": "MOOCs provide a unique opportunity for applying EDS due to the vast amounts of data they generate. Significant progress has been made in areas like engagement analysis, dropout prediction, and adaptive learning.", "classified": ["MOOCs应用", "EDS交互分析", "辍学率预测", "适应性学习"], "parent_cls_dict": {"MOOCs应用": "在线学习", "EDS交互分析": "数据分析", "辍学率预测": "教育结果", "适应性学习": "学习行为"}}, {"meta_info_type": "局限性", "meta_info_desc": "传统数据挖掘方法可能无法处理MOOC的大规模数据集", "meta_info_content": "Traditional data mining methods may not be sufficient for the massive datasets generated by MOOCs.", "classified": ["数据挖掘方法", "MOOC数据集"], "parent_cls_dict": {"数据挖掘方法": "数据分析", "MOOC数据集": "在线学习"}}], [{"meta_info_type": "学科领域", "meta_info_desc": "研究领域为教育过程挖掘 (Educational Process Mining)，属于教育数据挖掘的子领域", "meta_info_content": "Educational process mining (EPM) is an emerging field within educational data mining (EDM).", "classified": ["教育过程挖掘", "教育数据挖掘"], "parent_cls_dict": {"教育过程挖掘": "数据分析", "教育数据挖掘": "数据分析"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究涉及虚拟学习环境 (Virtual Learning Environments)、MOOCs、LMS、ITSs、AHSs", "meta_info_content": "Event logs may originate from systems like LMSs, MOOCs, ITSs, or AHSs.", "classified": ["虚拟学习环境", "MOOC平台", "学习管理系统", "智能辅导系统", "自适应超媒体系统"], "parent_cls_dict": {"虚拟学习环境": "在线学习", "MOOC平台": "在线学习", "学习管理系统": "学习管理", "智能辅导系统": "学习技术", "自适应超媒体系统": "学习技术"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在揭示教育过程中的隐性知识并促进更深入的理解", "meta_info_content": "Educational process mining (EPM) aims to make implicit knowledge explicit and facilitate a better understanding of educational processes.", "classified": ["隐性知识", "教育过程"], "parent_cls_dict": {"隐性知识": "教育研究", "教育过程": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "聚焦于事件日志分析、教育过程建模与可视化", "meta_info_content": "EPM can discover, analyze, and visualize complete educational processes.", "classified": ["事件日志分析", "教育过程建模", "可视化"], "parent_cls_dict": {"事件日志分析": "数据分析", "教育过程建模": "教育管理", "可视化": "数据分析"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于过程挖掘 (Process Mining) 技术，强调从事件日志中提取过程知识", "meta_info_content": "Process mining (PM) provides a bridge between data mining (DM) and process modeling, allowing for the discovery, monitoring, and improvement of processes using logs and audit trails.", "classified": ["过程挖掘", "事件日志", "过程知识"], "parent_cls_dict": {"过程挖掘": "数据科学", "事件日志": "数据科学", "过程知识": "知识管理"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源包括点击流、聊天日志、文档编辑历史、运动追踪、学习资源使用日志等", "meta_info_content": "Systems with tracking and logging capabilities gather temporal data like clickstreams, chat logs, document edit histories, motion tracking, and learning resource usage logs.", "classified": ["点击流", "聊天日志", "文档编辑历史", "运动追踪", "资源使用日志"], "parent_cls_dict": {"点击流": "系统数据", "聊天日志": "行为数据", "文档编辑历史": "系统数据", "运动追踪": "行为数据", "资源使用日志": "系统数据"}}, {"meta_info_type": "数据粒度", "meta_info_desc": "数据粒度从低级事件（如按键、鼠标手势）到高级学习活动", "meta_info_content": "These range from low-level events, such as keystrokes and mouse gestures, to higher-level events, such as students’ learning activities.", "classified": ["低级事件", "高级学习活动"], "parent_cls_dict": {"低级事件": "事件", "高级学习活动": "活动"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "包括Alpha算法、启发式挖掘、遗传算法、模糊挖掘等用于教育过程发现", "meta_info_content": "Common algorithms include Alpha Algorithm, Heuristic Miner, Genetic Algorithm, and <PERSON><PERSON> Miner.", "classified": ["Alpha算法", "启发式挖掘", "遗传算法", "模糊挖掘", "教育过程发现"], "parent_cls_dict": {"Alpha算法": "算法技术", "启发式挖掘": "数据挖掘", "遗传算法": "机器学习", "模糊挖掘": "数据挖掘", "教育过程发现": "教育分析"}}, {"meta_info_type": "模型评估方法", "meta_info_desc": "采用一致性检查评估行为与模型之间的偏差", "meta_info_content": "Conformance checking evaluates discrepancies between observed behaviors and modeled processes.", "classified": ["一致性检查"], "parent_cls_dict": {"一致性检查": "模型评估"}}, {"meta_info_type": "主要发现", "meta_info_desc": "EPM能够发现学生行为的完整模型，对教育过程进行分析和优化", "meta_info_content": "Unlike traditional EDM techniques, EPM emphasizes end-to-end processes, enabling the discovery of complete models of student behavior.", "classified": ["EPM", "学生行为模型", "教育过程优化"], "parent_cls_dict": {"EPM": "教育技术", "学生行为模型": "学习行为", "教育过程优化": "教育管理"}}, {"meta_info_type": "局限性", "meta_info_desc": "事件日志处理中存在噪声、不完整数据、时间戳不一致和隐私问题", "meta_info_content": "Challenges in handling event logs include issues like noise, incomplete data, timestamp inconsistencies, and privacy concerns.", "classified": ["事件日志处理", "数据噪声", "隐私问题"], "parent_cls_dict": {"事件日志处理": "数据管理", "数据噪声": "数据管理", "隐私问题": "隐私"}}], [{"meta_info_type": "学科领域", "meta_info_desc": "研究集中在学习分析领域", "meta_info_content": "Learning analytics (LA) is a transdisciplinary field that aims to uncover hidden knowledge from data generated by computer-based educational systems (CBES).", "classified": ["学习分析"], "parent_cls_dict": {"学习分析": "数据分析"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "使用学习管理系统(LMS)、智能辅导系统和大规模开放在线课程(MOOCs)", "meta_info_content": "It focuses on understanding diverse aspects of learning... leveraging tools such as learning management systems (LMS), intelligent tutoring systems, and massive open online courses (MOOCs).", "classified": ["学习管理系统", "智能辅导系统", "MOOC平台"], "parent_cls_dict": {"学习管理系统": "学习管理", "智能辅导系统": "学习技术", "MOOC平台": "在线学习"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在通过数据分析优化学习过程", "meta_info_content": "Learning analytics (LA) is a transdisciplinary field that aims to uncover hidden knowledge from data generated by computer-based educational systems (CBES).", "classified": ["数据分析", "学习优化"], "parent_cls_dict": {"数据分析": "数据分析", "学习优化": "教育研究"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注学习者行为、绩效、参与度和反馈机制", "meta_info_content": "It focuses on understanding diverse aspects of learning, such as learner behavior, performance, engagement, and feedback mechanisms.", "classified": ["学习者行为", "绩效", "参与度", "反馈机制"], "parent_cls_dict": {"学习者行为": "教育心理学", "绩效": "教育评估", "参与度": "教育心理学", "反馈机制": "教育方法"}}, {"meta_info_type": "理论基础", "meta_info_desc": "包括机器学习、自然语言处理和大数据等专门化范式", "meta_info_content": "LA is grounded in several key domains, including... specialized paradigms: Machine learning, natural language processing (NLP), and big data.", "classified": ["机器学习", "自然语言处理", "大数据"], "parent_cls_dict": {"机器学习": "人工智能", "自然语言处理": "人工智能", "大数据": "数据科学"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "使用预测分析识别有风险的学生并提供个性化支持", "meta_info_content": "LA models identify students at risk of failing or dropping out, enabling timely interventions.", "classified": ["预测分析", "学生风险识别", "个性化支持"], "parent_cls_dict": {"预测分析": "数据分析", "学生风险识别": "教育分析", "个性化支持": "教育技术"}}, {"meta_info_type": "主要发现", "meta_info_desc": "LA的演变、优势、挑战和风险被详细分类", "meta_info_content": "The taxonomy highlights the evolution, strengths, and challenges of LA: Strengths: A vibrant research community, diverse applications, and institutional support.", "classified": ["LA演变", "优势", "挑战", "风险"], "parent_cls_dict": {"LA演变": "数据分析", "优势": "教育结果", "挑战": "教育管理", "风险": "教育管理"}}, {"meta_info_type": "局限性", "meta_info_desc": "需要更强的理论基础和伦理考量", "meta_info_content": "Weaknesses: The need for robust theoretical foundations and ethical considerations.", "classified": ["理论基础", "伦理考量"], "parent_cls_dict": {"理论基础": "理论", "伦理考量": "伦理"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究涵盖初级教育、次级教育、高等教育，以及成人学习者", "meta_info_content": "Most research (58%) focused on tertiary education, followed by primary/secondary (25%) and adult learners (14%).", "classified": ["小学教育", "中学教育", "高等教育", "成人教育"], "parent_cls_dict": {"小学教育": "基础教育", "中学教育": "中学教育", "高等教育": "高等教育", "成人教育": "成人教育"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在外语学习与数据挖掘的交叉领域", "meta_info_content": "This study investigates how data mining (DM) is applied to enhance FLL (Foreign Language Learning).", "classified": ["外语学习", "数据挖掘"], "parent_cls_dict": {"外语学习": "语言学习", "数据挖掘": "数据分析"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究涉及传统学习环境和电子学习环境，包括移动学习与混合学习", "meta_info_content": "54% of studies were conducted in traditional settings, 45% in e-learning (including mobile and blended learning).", "classified": ["传统学习环境", "电子学习环境", "移动学习", "混合学习环境"], "parent_cls_dict": {"传统学习环境": "线下学习", "电子学习环境": "在线学习", "移动学习": "在线学习", "混合学习环境": "混合学习"}}, {"meta_info_type": "规模，实验参与人数", "meta_info_desc": "大多数研究使用的数据集规模少于1,000项", "meta_info_content": "76% of studies used datasets with fewer than 1,000 items.", "classified": ["数据集规模"], "parent_cls_dict": {"数据集规模": "规模"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在提供数据挖掘在外语学习中的应用概览，并识别研究的趋势与空白", "meta_info_content": "The goal is to synthesize current research trends and identify gaps, offering insights for researchers, educators, and advanced learners.", "classified": ["数据挖掘", "外语学习"], "parent_cls_dict": {"数据挖掘": "数据分析", "外语学习": "语言学习"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注数据挖掘对外语学习的适应性、预测性和行为分析作用", "meta_info_content": "DM offers significant opportunities to enhance FLL by personalizing instruction, predicting performance, and analyzing learner behaviors.", "classified": ["数据挖掘", "外语学习", "适应性", "预测性", "行为分析"], "parent_cls_dict": {"数据挖掘": "数据分析", "外语学习": "语言学习", "适应性": "教育技术", "预测性": "数据分析", "行为分析": "教育心理学"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于第二语言习得（SLA）和计算机辅助语言学习（CALL）的理论框架", "meta_info_content": "The convergence of SLA, CALL, and DM opens new possibilities for personalized and effective language learning.", "classified": ["第二语言习得", "计算机辅助语言学习"], "parent_cls_dict": {"第二语言习得": "语言学习", "计算机辅助语言学习": "语言学习"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源于2012至2017年间发表的208篇研究论文", "meta_info_content": "This study investigates how data mining (DM) is applied to enhance FLL by reviewing 208 research papers published between 2012 and 2017.", "classified": ["研究论文"], "parent_cls_dict": {"研究论文": "出版物"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "研究应用了因子分析、回归分析、文本挖掘、相关性挖掘和因果数据挖掘等方法", "meta_info_content": "The most common methods were: Factor Analysis, Regression, Text Mining, Correlation Mining, and Causal DM.", "classified": ["因子分析", "回归分析", "文本挖掘", "相关性挖掘", "因果数据挖掘"], "parent_cls_dict": {"因子分析": "统计分析", "回归分析": "回归分析", "文本挖掘": "数据挖掘", "相关性挖掘": "数据挖掘", "因果数据挖掘": "数据挖掘"}}, {"meta_info_type": "主要发现", "meta_info_desc": "数据挖掘在外语学习中的应用主要集中于学生表现预测、学习动机分析以及教师反馈提供", "meta_info_content": "Key applications included predicting student performance, analyzing learner motivation, and providing feedback for instructors.", "classified": ["外语学习数据挖掘", "学生表现预测", "学习动机分析", "教师反馈"], "parent_cls_dict": {"外语学习数据挖掘": "数据分析", "学生表现预测": "教育结果", "学习动机分析": "学习行为", "教师反馈": "教学"}}, {"meta_info_type": "局限性", "meta_info_desc": "数据挖掘在外语学习中的应用有限，特别是在移动学习和初级及次级教育中", "meta_info_content": "DM is underutilized in FLL, particularly in primary and secondary education. Despite the availability of large datasets from apps like Duolingo and Busuu, most studies used small datasets.", "classified": ["数据挖掘应用", "外语学习"], "parent_cls_dict": {"数据挖掘应用": "数据分析", "外语学习": "语言学习"}}], [{"meta_info_type": "研究对象年龄段 / 教育阶段", "meta_info_desc": "研究涵盖传统课堂、混合学习系统等多种教育环境中的学生", "meta_info_content": "Different types of environments (e.g., traditional classrooms, blended learning systems) provide diverse data sources.", "classified": ["不确定教育阶段"], "parent_cls_dict": {"不确定教育阶段": "不确定教育阶段"}}, {"meta_info_type": "学科领域", "meta_info_desc": "研究集中在教育数据挖掘和学习分析领域", "meta_info_content": "This survey is an updated and improved version of the previous one published in 2013 with the title 'Data Mining in Education.' It reviews how Educational Data Mining (EDM) and Learning Analytics (LA) have been applied over educational data.", "classified": ["教育数据挖掘", "学习分析"], "parent_cls_dict": {"教育数据挖掘": "数据分析", "学习分析": "数据分析"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究涵盖传统课堂、LMS、ITS和MOOCs等多种平台", "meta_info_content": "Systems such as Learning Management Systems (LMS), Intelligent Tutoring Systems (ITS), Massive Open Online Courses (MOOCs), and adaptive hypermedia systems store detailed data on student activities and interactions.", "classified": ["传统课堂", "学习管理系统", "智能辅导系统", "MOOC平台"], "parent_cls_dict": {"传统课堂": "线下学习", "学习管理系统": "学习管理", "智能辅导系统": "学习技术", "MOOC平台": "在线学习"}}, {"meta_info_type": "研究目的", "meta_info_desc": "综述教育数据挖掘和学习分析领域的最新进展和未来趋势", "meta_info_content": "This paper provides the current state of the art by reviewing the main publications, the key milestones, the knowledge discovery cycle, the main educational environments, the specific tools, the freely available datasets, the most used methods, the main objectives, and the future trends in this research area.", "classified": ["教育数据挖掘", "学习分析"], "parent_cls_dict": {"教育数据挖掘": "数据分析", "学习分析": "数据分析"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注教育数据挖掘（EDM）与学习分析（LA）", "meta_info_content": "Educational Data Mining (EDM) focuses on developing methods for exploring the unique types of data that come from educational environments. Learning Analytics (LA) focuses on the measurement, collection, analysis, and reporting of data about learners and their contexts.", "classified": ["教育数据挖掘", "学习分析"], "parent_cls_dict": {"教育数据挖掘": "数据分析", "学习分析": "数据分析"}}, {"meta_info_type": "理论基础", "meta_info_desc": "基于知识发现与数据挖掘（KDD）过程的循环应用", "meta_info_content": "The process of applying EDM and LA involves a cyclic application of the general Knowledge Discovery and Data Mining (KDD) process.", "classified": ["知识发现", "数据挖掘"], "parent_cls_dict": {"知识发现": "数据科学", "数据挖掘": "数据科学"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据包括学生互动、人口统计信息和情绪状态等", "meta_info_content": "Data are gathered from various sources, including student interactions, demographic information, and emotional states.", "classified": ["互动", "人口统计", "情绪状态"], "parent_cls_dict": {"互动": "行为数据", "人口统计": "人口数据", "情绪状态": "个人数据"}}, {"meta_info_type": "数据粒度", "meta_info_desc": "数据粒度多样，包括学生活动的详细信息", "meta_info_content": "Web-based educational systems accumulate a huge amount of potential data from multiple sources with different formats and levels of granularity.", "classified": ["学生活动"], "parent_cls_dict": {"学生活动": "活动"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "采用聚类、预测、社交网络分析等方法", "meta_info_content": "Methods commonly used in EDM/LA include clustering, prediction, and social network analysis.", "classified": ["聚类", "预测", "社交网络分析"], "parent_cls_dict": {"聚类": "聚类算法", "预测": "数据分析", "社交网络分析": "网络分析"}}, {"meta_info_type": "Type of Output/Artifact", "meta_info_desc": "研究产出为教育数据挖掘与学习分析工具和方法", "meta_info_content": "Several specific tools have been developed to facilitate EDM and LA research, including DataShop, GISMO, and Inspire.", "classified": ["工具", "方法"], "parent_cls_dict": {"工具": "技术工具", "方法": "方法论"}}, {"meta_info_type": "目标用户", "meta_info_desc": "目标用户包括学生、教育者、管理者和研究人员", "meta_info_content": "Stakeholders include students, educators, administrators, and researchers.", "classified": ["学生", "教育者", "管理员", "研究人员"], "parent_cls_dict": {"学生": "学习者", "教育者": "教育相关人员", "管理员": "管理相关人员", "研究人员": "研究相关人员"}}, {"meta_info_type": "主要发现", "meta_info_desc": "EDM和LA已成为跨学科研究领域，具有广泛应用", "meta_info_content": "EDM and LA have matured into interdisciplinary research areas with substantial growth in conferences, journals, and publications.", "classified": ["EDM", "LA", "跨学科研究"], "parent_cls_dict": {"EDM": "数据分析", "LA": "数据分析", "跨学科研究": "学术合作"}}, {"meta_info_type": "局限性", "meta_info_desc": "需开发通用工具并解决数据文化与隐私问题", "meta_info_content": "Challenges remain, including the need for general-purpose EDM/LA tools, the development of a data-driven culture, and addressing ethical and privacy concerns.", "classified": ["通用工具", "数据文化", "隐私问题"], "parent_cls_dict": {"通用工具": "技术", "数据文化": "数据管理", "隐私问题": "隐私"}}], [{"meta_info_type": "学科领域", "meta_info_desc": "研究集中在教育数据挖掘领域", "meta_info_content": "Educational Data Mining (EDM) has been defined by <PERSON> et al. (2010) as 'the area of scientific inquiry centered around the development of methods for making discoveries within the unique types of data that come from educational settings.'", "classified": ["教育数据挖掘"], "parent_cls_dict": {"教育数据挖掘": "数据分析"}}, {"meta_info_type": "学习环境与平台", "meta_info_desc": "研究涉及MOOCs等在线教育平台", "meta_info_content": "Notable contributions include the PSLC DataShop (<PERSON><PERSON><PERSON> et al., 2010) and the emergence of new datasets through MOOCs (Massive Open Online Courses) and other platforms.", "classified": ["MOOC平台", "在线教育平台"], "parent_cls_dict": {"MOOC平台": "在线学习", "在线教育平台": "在线学习"}}, {"meta_info_type": "研究目的", "meta_info_desc": "旨在系统回顾教育数据挖掘领域公开数据集的结构、类型及用途", "meta_info_content": "This paper provides a systematic review of publicly available EDM datasets, aiming to inform new researchers and experienced practitioners about the resources available for conducting EDM tasks.", "classified": ["教育数据挖掘", "公开数据集"], "parent_cls_dict": {"教育数据挖掘": "数据分析", "公开数据集": "数据分析"}}, {"meta_info_type": "核心关注概念", "meta_info_desc": "关注公开教育数据集的分类、任务及未来要求", "meta_info_content": "The review categorizes datasets into general-purpose repositories, datasets used in competitions, and standalone datasets, and highlights the need for benchmarking and reproducibility in the EDM domain.", "classified": ["公开教育数据集", "分类", "任务", "未来要求"], "parent_cls_dict": {"公开教育数据集": "数据分析", "分类": "数据分析", "任务": "教育管理", "未来要求": "教育管理"}}, {"meta_info_type": "数据来源", "meta_info_desc": "数据来源包括UCI机器学习库、Mendeley数据仓储、Harvard Dataverse及CMU DataShop", "meta_info_content": "Sources include the UCI Machine Learning Repository, the Mendeley Data Repository, Harvard Dataverse, and DataShop@CMU.", "classified": ["数据仓储", "数据集"], "parent_cls_dict": {"数据仓储": "数据仓储", "数据集": "数据集"}}, {"meta_info_type": "数据粒度", "meta_info_desc": "数据粒度涵盖从个人学术记录到大规模课程数据", "meta_info_content": "Examples include the HarvardX Person-Course Dataset with 338,223 instances and the Student Performance Dataset with 649 instances.", "classified": ["个人学术记录", "大规模课程数据"], "parent_cls_dict": {"个人学术记录": "记录", "大规模课程数据": "数据"}}, {"meta_info_type": "分析技术算法", "meta_info_desc": "利用数据集进行分类、聚类及预测任务", "meta_info_content": "Tasks include classification and clustering tasks using datasets such as the Educational Process Mining Dataset and final grade prediction using the Student Performance Dataset.", "classified": ["分类", "聚类", "预测"], "parent_cls_dict": {"分类": "分类技术", "聚类": "聚类算法", "预测": "数据分析"}}, {"meta_info_type": "Type of Output/Artifact", "meta_info_desc": "提供了公开数据集的系统化分类和任务导向的总结", "meta_info_content": "This review identifies 41 publicly available EDM datasets categorized into three types: general-purpose repositories, competition datasets, and standalone datasets.", "classified": ["分类", "总结"], "parent_cls_dict": {"分类": "分类", "总结": "总结"}}, {"meta_info_type": "目标用户", "meta_info_desc": "目标用户为教育数据挖掘领域的研究者和实践者", "meta_info_content": "The review aims to inform new researchers and experienced practitioners about the resources available for conducting EDM tasks.", "classified": ["研究人员", "实践者"], "parent_cls_dict": {"研究人员": "研究相关人员", "实践者": "实践相关人员"}}, {"meta_info_type": "主要发现", "meta_info_desc": "发现数据类型多样，任务集中于预测成绩、辍学等", "meta_info_content": "Key findings include data types such as demographic and multimodal data, and tasks like final grade prediction, dropout detection, and topic modeling.", "classified": ["数据类型", "成绩预测", "辍学预测"], "parent_cls_dict": {"数据类型": "数据分析", "成绩预测": "教育结果", "辍学预测": "教育结果"}}, {"meta_info_type": "局限性", "meta_info_desc": "未提出新数据集，仅限于已有数据的整理与分类", "meta_info_content": "The review does not introduce new datasets but focuses on summarizing and categorizing existing publicly available datasets.", "classified": ["数据集限制", "数据整理"], "parent_cls_dict": {"数据集限制": "数据管理", "数据整理": "数据管理"}}]]